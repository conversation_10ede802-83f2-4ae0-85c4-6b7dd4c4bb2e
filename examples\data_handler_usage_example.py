#!/usr/bin/env python3
"""
数据获取Handler使用示例

展示如何使用两种数据获取方式：
1. 从服务器查询 (需要api_token) - ServerDataHandler
2. 从多维表获取 (使用jss-api-extend) - BitableDataHandler

以及如何使用PipelineContext进行上下文传递
"""

import sys
import logging
import os
import time

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.context import ClientConfig
from core.handlers import (
    # 数据获取Handler
    DataHandler,
    ServerDataHandler,
    BitableDataHandler,
    DingTalkDataHandler,
    
    # Pipeline上下文
    PipelineContext,
    PipelineMetrics,
    
    # 工厂函数
    create_data_handler,
    create_pipeline_context,
    build_chain
)


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('data_handler_example')


def example_1_server_data_handler():
    """
    示例1：使用服务器数据获取Handler
    """
    print("=" * 60)
    print("示例1：从服务器查询数据 (ServerDataHandler)")
    print("=" * 60)
    
    logger = setup_logging()
    
    # 1. 创建配置 - 包含api_token
    config_data = {
        'name': 'server-client',
        'user_info': {'api-token': 'your-api-token-here'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'},
        'data_fetcher_config': {
            'platform': 'xhs',  # 查询小红书平台
            'limit': 50,
            'filter_rules': {
                'update_times_limit': 3  # 最多更新3次
            }
        }
    }
    config = ClientConfig.from_dict(config_data)
    
    # 2. 创建Pipeline上下文
    context = create_pipeline_context(config, f"server-batch-{int(time.time())}")
    
    # 3. 创建服务器数据Handler
    server_handler = ServerDataHandler(config, logger)
    
    print("✅ 创建服务器数据Handler")
    print(f"   数据源: 服务器API")
    print(f"   平台: {config_data['data_fetcher_config']['platform']}")
    print(f"   限制: {config_data['data_fetcher_config']['limit']} 条")
    print(f"   批次ID: {context.batch_id}")
    
    # 4. 模拟处理（实际使用时会调用handle方法）
    try:
        # context = server_handler.handle(context)
        print("   注意: 需要有效的api-token和服务器连接才能实际获取数据")
    except Exception as e:
        print(f"   预期错误: {e}")
    
    return server_handler, context


def example_2_bitable_data_handler():
    """
    示例2：使用多维表格数据获取Handler
    """
    print("\n" + "=" * 60)
    print("示例2：从多维表格获取数据 (BitableDataHandler)")
    print("=" * 60)
    
    logger = setup_logging()
    
    # 1. 创建配置 - 不包含api_token，使用多维表格
    config_data = {
        'name': 'bitable-client',
        'user_info': {'dingtalk-app-key': 'your-app-key', 'dingtalk-app-secret': 'your-app-secret'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'},
        'bitable_config': {
            'dentryUuid': 'test-uuid',
            'idOrName': 'test-table'
        },
        'data_fetcher_config': {
            'limit': 100
        }
    }
    config = ClientConfig.from_dict(config_data)
    
    # 2. 创建Pipeline上下文
    context = create_pipeline_context(config, f"bitable-batch-{int(time.time())}")
    
    # 3. 创建多维表格数据Handler
    bitable_handler = BitableDataHandler(config, logger)
    
    print("✅ 创建多维表格数据Handler")
    print(f"   数据源: 钉钉多维表格")
    print(f"   表格: {config_data['bitable']['dentryUuid']}/{config_data['bitable']['idOrName']}")
    print(f"   限制: {config_data['data_fetcher_config']['limit']} 条")
    print(f"   批次ID: {context.batch_id}")
    
    # 4. 模拟处理
    try:
        # context = bitable_handler.handle(context)
        print("   注意: 需要有效的钉钉配置才能实际获取数据")
    except Exception as e:
        print(f"   预期错误: {e}")
    
    return bitable_handler, context


def example_3_explicit_data_source():
    """
    示例3：明确指定数据源类型
    """
    print("\n" + "=" * 60)
    print("示例3：明确指定数据源类型")
    print("=" * 60)

    logger = setup_logging()

    # 测试配置
    config = ClientConfig.from_dict({
        'name': 'explicit-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    })

    # 明确指定数据源类型
    server_handler = create_data_handler(config, logger, "server")
    bitable_handler = create_data_handler(config, logger, "bitable")

    # 或者使用专门的创建函数
    server_handler2 = create_server_data_handler(config, logger)
    bitable_handler2 = create_bitable_data_handler(config, logger)

    print("✅ 明确指定数据源类型")
    print(f"   create_data_handler(config, logger, 'server'): {server_handler.__class__.__name__}")
    print(f"   create_data_handler(config, logger, 'bitable'): {bitable_handler.__class__.__name__}")
    print(f"   create_server_data_handler(config, logger): {server_handler2.__class__.__name__}")
    print(f"   create_bitable_data_handler(config, logger): {bitable_handler2.__class__.__name__}")

    # 测试错误的数据源类型
    try:
        wrong_handler = create_data_handler(config, logger, "invalid")
    except ValueError as e:
        print(f"✅ 错误处理: {e}")

    return server_handler, bitable_handler


def example_4_custom_bitable_handler():
    """
    示例4：自定义多维表格Handler
    """
    print("\n" + "=" * 60)
    print("示例4：自定义多维表格Handler")
    print("=" * 60)
    
    logger = setup_logging()
    
    # 1. 继承BitableDataHandler，自定义字段映射
    class MyCustomBitableHandler(BitableDataHandler):
        """我的自定义多维表格Handler"""
        
        def _get_filter_conditions(self, context):
            """自定义过滤条件"""
            return [
                {"field": "状态", "operator": "equal", "value": ["待处理"]},
                {"field": "平台", "operator": "equal", "value": ["小红书", "抖音"]},
                {"field": "更新次数", "operator": "less", "value": [3]}
            ]
        
        def _extract_work_url(self, fields):
            """自定义URL提取逻辑"""
            # 我的表格中URL字段叫"作品地址"
            url_field = fields.get('作品地址', fields.get('链接', ''))
            if isinstance(url_field, list) and url_field:
                return url_field[0].get('text', '') if isinstance(url_field[0], dict) else str(url_field[0])
            return str(url_field) if url_field else None
        
        def _extract_submit_user(self, fields):
            """自定义用户提取逻辑"""
            # 我的表格中用户字段叫"创建者"
            user_field = fields.get('创建者', fields.get('提交人', []))
            if isinstance(user_field, list):
                return [user.get('name', str(user)) if isinstance(user, dict) else str(user) for user in user_field]
            return [str(user_field)] if user_field else []
    
    # 2. 创建配置
    config = ClientConfig.from_dict({
        'name': 'custom-bitable-client',
        'user_info': {'dingtalk-app-key': 'test-key'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'},
        'bitable_config': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    })
    
    # 3. 创建自定义Handler
    custom_handler = MyCustomBitableHandler(config, logger)
    
    print("✅ 创建自定义多维表格Handler")
    print("   自定义功能:")
    print("   - 自定义过滤条件（状态、平台、更新次数）")
    print("   - 自定义字段映射（作品地址、创建者）")
    print("   - 自定义数据提取逻辑")
    
    return custom_handler


def example_5_pipeline_context_usage():
    """
    示例5：Pipeline上下文的使用
    """
    print("\n" + "=" * 60)
    print("示例5：Pipeline上下文的使用")
    print("=" * 60)
    
    logger = setup_logging()
    
    # 1. 创建配置
    config = ClientConfig.from_dict({
        'name': 'pipeline-demo-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    })
    
    # 2. 创建Pipeline上下文
    context = create_pipeline_context(config, "demo-batch-001")
    
    print("✅ Pipeline上下文创建")
    print(f"   客户名称: {context.client_name}")
    print(f"   批次ID: {context.batch_id}")
    print(f"   开始时间: {context.start_time}")
    
    # 3. 模拟数据获取阶段
    context.total_tasks = 10
    context.data_source_type = "server"
    context.record_stage_time('data', 1.5)
    
    # 4. 模拟更新阶段
    context.successful_updates = 8
    context.failed_updates = 2
    context.add_error('update', 'Network timeout', 'work-123', {'retry_count': 3})
    context.record_stage_time('update', 5.2)
    
    # 5. 模拟通知阶段
    context.notifications_sent = 3
    context.record_stage_time('notify', 0.8)
    
    # 6. 模拟同步阶段
    context.successful_syncs = 8
    context.failed_syncs = 0
    context.record_stage_time('sync', 2.1)
    
    # 7. 获取执行摘要
    summary = context.get_summary()
    
    print("\n✅ 执行摘要:")
    print(f"   总任务数: {summary['total_tasks']}")
    print(f"   成功更新: {summary['successful_updates']}")
    print(f"   失败更新: {summary['failed_updates']}")
    print(f"   发送通知: {summary['notifications_sent']}")
    print(f"   成功同步: {summary['successful_syncs']}")
    print(f"   总错误数: {summary['total_errors']}")
    print(f"   总执行时间: {summary['total_time']}")
    
    # 8. Pipeline指标收集
    metrics = PipelineMetrics()
    metrics.add_context(context)
    
    overall_stats = metrics.get_overall_stats()
    print("\n✅ 整体统计:")
    print(f"   总批次数: {overall_stats['total_batches']}")
    print(f"   成功率: {overall_stats['success_rate']:.1f}%")
    
    return context, metrics


def example_6_complete_chain_with_pipeline():
    """
    示例6：完整的处理链与Pipeline上下文
    """
    print("\n" + "=" * 60)
    print("示例6：完整的处理链与Pipeline上下文")
    print("=" * 60)
    
    logger = setup_logging()
    
    # 1. 创建配置
    config = ClientConfig.from_dict({
        'name': 'complete-demo-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    })
    
    # 2. 创建Pipeline上下文
    context = create_pipeline_context(config)
    
    # 3. 构建完整的处理链（明确指定数据源）
    chain = build_chain(config, logger, data_source='server', chain_type='standard')
    
    print("✅ 完整处理链创建")
    print("   链结构: 数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步")
    print(f"   Pipeline上下文: {context.batch_id}")
    
    # 4. 模拟执行（实际使用时）
    try:
        # result_context = chain.handle(context)
        print("   注意: 实际执行需要有效的配置和网络连接")
    except Exception as e:
        print(f"   预期错误: {e}")
    
    return chain, context


def main():
    """Main function demonstrating all examples."""
    print("🚀 数据获取Handler使用示例")
    
    try:
        # 运行所有示例
        example_1_server_data_handler()
        example_2_bitable_data_handler()
        example_3_explicit_data_source()
        example_4_custom_bitable_handler()
        example_5_pipeline_context_usage()
        example_6_complete_chain_with_pipeline()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行成功！")
        print("=" * 60)
        print()
        print("📚 数据获取方式总结:")
        print("   1. ServerDataHandler - 从服务器查询（需要api-token）")
        print("   2. BitableDataHandler - 从多维表格获取（使用jss-api-extend）")
        print("   3. 明确指定 - 客户必须明确指定数据源类型")
        print("   4. 自定义Handler - 继承并重写方法实现自定义逻辑")
        print()
        print("🔧 Pipeline上下文功能:")
        print("   1. 跨Handler传递数据和状态")
        print("   2. 记录执行时间和错误信息")
        print("   3. 生成执行摘要和统计信息")
        print("   4. 支持批次管理和指标收集")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
