# 数据源使用指南

## 📋 概述

系统支持两种数据获取方式，**每个客户必须明确指定数据源类型**：

1. **ServerDataHandler** - 从服务器查询（需要api-token）
2. **BitableDataHandler** - 从多维表格获取（使用jss-api-extend）

## 🔧 使用方式

### 1. 明确指定数据源类型

```python
from core.handlers import create_data_handler, build_chain

# 方式1：使用通用函数，明确指定类型
server_handler = create_data_handler(config, logger, "server")
bitable_handler = create_data_handler(config, logger, "bitable")

# 方式2：使用专门的创建函数
from core.handlers import create_server_data_handler, create_bitable_data_handler

server_handler = create_server_data_handler(config, logger)
bitable_handler = create_bitable_data_handler(config, logger)

# 方式3：构建完整处理链时指定
chain = build_chain(config, logger, data_source="server", chain_type="standard")
```

### 2. 错误处理

```python
# 错误：不指定数据源类型
try:
    handler = create_data_handler(config, logger)  # ❌ 缺少data_source参数
except TypeError as e:
    print(f"错误: {e}")

# 错误：指定无效的数据源类型
try:
    handler = create_data_handler(config, logger, "invalid")  # ❌ 无效类型
except ValueError as e:
    print(f"错误: {e}")

# 正确：明确指定有效的数据源类型
handler = create_data_handler(config, logger, "server")  # ✅ 正确
```

## 🔧 ServerDataHandler - 服务器数据源

### 适用场景
- 客户有自己的服务器API
- 需要从中央服务器查询待处理任务
- 有api-token认证

### 配置要求
```python
config_data = {
    'name': 'server-client',
    'user_info': {
        'api-token': 'your-api-token-here'  # 必需
    },
    'data_fetcher_config': {
        'platform': 'xhs',  # 查询平台：'xhs', 'dy', 'ks', 'all'
        'limit': 100,        # 查询数量限制
        'filter_rules': {
            'update_times_limit': 3  # 更新次数限制
        }
    }
}
```

### 使用示例
```python
from core.handlers import create_server_data_handler, create_pipeline_context

# 1. 创建配置
config = ClientConfig.from_dict(config_data)

# 2. 创建Handler
handler = create_server_data_handler(config, logger)

# 3. 创建Pipeline上下文
context = create_pipeline_context(config)

# 4. 处理数据
result_context = handler.handle(context)

print(f"从服务器获取了 {result_context.total_tasks} 个任务")
print(f"数据源类型: {result_context.data_source_type}")  # "server"
```

### 数据流程
```
客户配置(api-token) -> ServerDataHandler -> 调用服务器API -> 
获取原始数据 -> 转换为WorkDetailTaskModel -> 应用过滤规则 -> 
设置到context.pending_tasks
```

## 🔧 BitableDataHandler - 多维表格数据源

### 适用场景
- 使用钉钉多维表格存储数据
- 每个客户的字段映射不同
- 需要自定义过滤条件

### 配置要求
```python
config_data = {
    'name': 'bitable-client',
    'user_info': {
        'dingtalk-app-key': 'your-app-key',
        'dingtalk-app-secret': 'your-app-secret'
    },
    'bitable': {
        'dentryUuid': 'your-table-uuid',  # 必需
        'idOrName': 'your-table-name'     # 必需
    },
    'bitable_config': {
        'dentryUuid': 'your-table-uuid',
        'idOrName': 'your-table-name'
    },
    'data_fetcher_config': {
        'limit': 100
    }
}
```

### 使用示例
```python
from core.handlers import create_bitable_data_handler, BitableDataHandler

# 1. 基础使用
handler = create_bitable_data_handler(config, logger)

# 2. 自定义Handler（推荐）
class MyBitableHandler(BitableDataHandler):
    def _get_filter_conditions(self, context):
        """自定义过滤条件"""
        return [
            {"field": "状态", "operator": "equal", "value": ["待处理"]},
            {"field": "平台", "operator": "equal", "value": ["小红书", "抖音"]},
            {"field": "更新次数", "operator": "less", "value": [3]}
        ]
    
    def _extract_work_url(self, fields):
        """自定义URL字段映射"""
        # 我的表格中URL字段叫"作品地址"
        return fields.get('作品地址', fields.get('链接', ''))
    
    def _extract_submit_user(self, fields):
        """自定义用户字段映射"""
        # 我的表格中用户字段叫"创建者"
        user_field = fields.get('创建者', fields.get('提交人', []))
        if isinstance(user_field, list):
            return [user.get('name', str(user)) if isinstance(user, dict) else str(user) for user in user_field]
        return [str(user_field)] if user_field else []

# 使用自定义Handler
custom_handler = MyBitableHandler(config, logger)
```

### 数据流程
```
客户配置(bitable) -> BitableDataHandler -> 调用jss-api-extend -> 
查询多维表格 -> 转换字段映射 -> 转换为WorkDetailTaskModel -> 
应用过滤规则 -> 设置到context.pending_tasks
```

## 🔄 完整的处理链

### 标准链
```python
# 包含所有4个Handler：数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步
chain = build_chain(config, logger, data_source="server", chain_type="standard")
```

### 基础链
```python
# 不包含通知：数据获取 -> 更新逻辑 -> 数据同步
chain = build_chain(config, logger, data_source="bitable", chain_type="basic")
```

### 监控链
```python
# 不包含同步：数据获取 -> 更新逻辑 -> 通知逻辑
chain = build_chain(config, logger, data_source="server", chain_type="monitoring")
```

### 自定义链
```python
# 完全自定义
data_handler = create_data_handler(config, logger, "bitable")
update_handler = create_update_handler(config, logger, use_database=True)
notify_handler = create_notify_handler(config, logger)

# 构建链
chain = data_handler.set_next(update_handler).set_next(notify_handler)
```

## 📊 Pipeline上下文

### 创建和使用
```python
from core.handlers import create_pipeline_context

# 创建上下文
context = create_pipeline_context(config, batch_id="my-batch-001")

# 执行处理链
result_context = chain.handle(context)

# 获取执行摘要
summary = result_context.get_summary()
print(f"处理了 {summary['total_tasks']} 个任务")
print(f"成功率: {summary['successful_updates']}/{summary['total_tasks']}")
print(f"数据源: {summary['data_source_type']}")
print(f"执行时间: {summary['total_time']}")
```

### 上下文数据
```python
# 数据获取阶段
context.raw_data              # 原始数据
context.pending_tasks         # 待处理任务
context.total_tasks          # 任务总数
context.data_source_type     # "server" 或 "bitable"

# 更新阶段
context.updated_works        # 更新后的作品
context.successful_updates   # 成功更新数
context.failed_updates       # 失败更新数

# 通知阶段
context.notifications_sent   # 发送通知数

# 同步阶段
context.successful_syncs     # 成功同步数
context.failed_syncs         # 失败同步数
```

## ⚠️ 重要变更

### 已移除的功能
- ❌ 自动检测数据源类型
- ❌ `create_data_handler(config, logger)` 不带data_source参数
- ❌ `build_chain(config, logger, 'standard')` 不带data_source参数

### 新的要求
- ✅ 必须明确指定数据源类型：`"server"` 或 `"bitable"`
- ✅ 使用 `create_data_handler(config, logger, "server")` 
- ✅ 使用 `build_chain(config, logger, "server", "standard")`

### 迁移指南
```python
# 旧方式 ❌
chain = build_chain(config, logger, 'standard')

# 新方式 ✅
chain = build_chain(config, logger, data_source="server", chain_type="standard")
# 或
chain = build_chain(config, logger, data_source="bitable", chain_type="standard")
```

## 🎯 最佳实践

### 1. 选择数据源
- **有服务器API + api-token** → 使用 `"server"`
- **使用钉钉多维表格** → 使用 `"bitable"`

### 2. 自定义Handler
- **服务器数据源** → 一般不需要自定义，直接使用 `ServerDataHandler`
- **多维表格数据源** → 建议继承 `BitableDataHandler` 实现字段映射

### 3. 错误处理
```python
try:
    handler = create_data_handler(config, logger, data_source)
    context = create_pipeline_context(config)
    result = handler.handle(context)
except ValueError as e:
    logger.error(f"配置错误: {e}")
except Exception as e:
    logger.error(f"处理失败: {e}")
```

### 4. 日志记录
```python
logger.info(f"使用数据源: {data_source}")
logger.info(f"批次ID: {context.batch_id}")
logger.info(f"处理结果: {result.get_summary()}")
```

## 🔧 客户实现示例

每个客户根据自己的数据源类型选择合适的方式：

### 水云兔客户（使用服务器）
```python
# 水云兔有自己的服务器API
chain = build_chain(config, logger, data_source="server", chain_type="standard")
```

### 淘天客户（使用多维表格）
```python
# 淘天使用钉钉多维表格，需要自定义字段映射
class TaotianBitableHandler(BitableDataHandler):
    def _extract_work_url(self, fields):
        return fields.get('商品链接', '')  # 淘天的字段名

handler = TaotianBitableHandler(config, logger)
```

这样每个客户都必须明确指定数据源，避免了配置错误和意外行为。
