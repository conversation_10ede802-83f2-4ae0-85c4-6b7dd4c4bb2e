# -*- coding: utf-8 -*-
"""
Adapter module for social media platform APIs.

This module provides handlers for various social media platforms:
- <PERSON><PERSON><PERSON> (TikTok China): Detail and search handlers
- <PERSON><PERSON><PERSON> (Little Red Book): Detail and comment handlers
- <PERSON><PERSON>hou: Detail handler
- TikTok: Search handler
"""

from .douyin_detail_handler import DouyinDetailHandler
from .douyin_search_handler import DouyinSearchHandler
from .douyin_comment_handler import DouyinCommentHandler
from .kuaishou_detail_handler import KuaiShouDetailHandler
from .wxvideo_handler import WxVideoHandler
from .xhs_detail_handler import XhsDetailHandler
from .xhs_comment_handler import XhsCommentHandler
from .tiktok_search_handler import TiktokSearchHandler
from .douyin_user_handler import <PERSON>uyin<PERSON><PERSON><PERSON>andler

__all__ = [
    "DouyinDetailHandler",
    "DouyinSearchHandler",
    "<PERSON><PERSON>inCommentHand<PERSON>",
    "KuaiShouDetailHand<PERSON>",
    "XhsDetailHandler",
    "XhsCommentHand<PERSON>",
    "TiktokSearchHandler",
    "WxVideoHand<PERSON>",
    "<PERSON>uy<PERSON><PERSON>serHandler"
]
