"""
Main entry point for the work monitor system.

This module provides the command-line interface and main execution logic
for the work monitor system.
"""

import asyncio
import logging
import sys
from typing import Optional

import click

from core.exceptions import WorkMonitorException, ConfigurationException
from services.config_manager import ConfigManager
from config.base_config import BaseConfig


def setup_logging(log_level: str = "INFO", client_name: Optional[str] = None) -> logging.Logger:
    """
    Set up logging configuration for the application.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        client_name: Optional client name for client-specific logging

    Returns:
        Configured logger instance
    """
    base_config = BaseConfig()

    # Configure handlers
    handlers = [logging.StreamHandler(sys.stdout)]

    if client_name:
        # Client-specific log file
        log_file = base_config.get_log_file_path(client_name)
        handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
        logger_name = f'work_monitor.{client_name}'
    else:
        # Common log file
        log_file = base_config.get_common_log_file_path()
        handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
        logger_name = 'work_monitor'

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers,
        force=True  # Override any existing configuration
    )

    return logging.getLogger(logger_name)


@click.group()
@click.option('--log-level', default='INFO', help='Set the logging level')
@click.pass_context
def cli(ctx, log_level):
    """Work Monitor System - Chain-of-Responsibility Architecture."""
    ctx.ensure_object(dict)
    ctx.obj['logger'] = setup_logging(log_level)


@cli.command()
@click.option('--client', help='Specific client to run (optional)')
@click.option('--dry-run', is_flag=True, help='Run in dry-run mode without making changes')
@click.pass_context
def run(ctx, client: Optional[str], dry_run: bool):
    """Run the work monitor system."""
    logger = ctx.obj['logger']

    try:
        logger.info("Starting work monitor system")

        if dry_run:
            logger.info("Running in dry-run mode")

        # Initialize configuration manager
        config_manager = ConfigManager()

        if client:
            logger.info(f"Running for specific client: {client}")

            # Validate client exists
            client_config = config_manager.get_client_config(client)
            if not client_config:
                logger.error(f"Client configuration not found: {client}")
                sys.exit(1)

            # Validate client configuration
            validation_errors = config_manager.validate_client_config(client)
            if validation_errors:
                logger.error(f"Client configuration validation failed for {client}:")
                for error in validation_errors:
                    logger.error(f"  - {error}")
                sys.exit(1)

            # TODO: Execute for specific client
            logger.info(f"Client {client} configuration is valid")

        else:
            logger.info("Running for all active clients")

            # Get all active clients
            active_clients = config_manager.get_active_clients()
            if not active_clients:
                logger.warning("No active clients found")
                return

            logger.info(f"Found {len(active_clients)} active clients: {', '.join(active_clients)}")

            # Validate all client configurations
            validation_results = config_manager.validate_all_configs()
            if validation_results:
                logger.error("Configuration validation failed for some clients:")
                for client_name, errors in validation_results.items():
                    logger.error(f"  {client_name}:")
                    for error in errors:
                        logger.error(f"    - {error}")
                sys.exit(1)

            # TODO: Execute for all clients
            logger.info("All client configurations are valid")

        logger.info("Work monitor system completed successfully")

    except ConfigurationException as e:
        logger.error(f"Configuration error: {e.message}")
        if e.details:
            logger.error(f"Error details: {e.details}")
        sys.exit(1)
    except WorkMonitorException as e:
        logger.error(f"Work monitor error: {e.message}")
        if e.details:
            logger.error(f"Error details: {e.details}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option('--client', help='Validate specific client configuration (optional)')
@click.pass_context
def validate(ctx, client: Optional[str]):
    """Validate system configuration."""
    logger = ctx.obj['logger']

    try:
        logger.info("Validating system configuration")

        # Initialize configuration manager
        config_manager = ConfigManager()

        if client:
            logger.info(f"Validating configuration for client: {client}")

            # Check if client exists
            if client not in config_manager.get_all_client_names():
                logger.error(f"Client not found: {client}")
                sys.exit(1)

            # Validate specific client
            validation_errors = config_manager.validate_client_config(client)
            if validation_errors:
                logger.error(f"Configuration validation failed for {client}:")
                for error in validation_errors:
                    logger.error(f"  - {error}")
                sys.exit(1)
            else:
                logger.info(f"Configuration for {client} is valid")
        else:
            logger.info("Validating all client configurations")

            # Validate all configurations
            validation_results = config_manager.validate_all_configs()

            if validation_results:
                logger.error("Configuration validation failed for some clients:")
                for client_name, errors in validation_results.items():
                    logger.error(f"  {client_name}:")
                    for error in errors:
                        logger.error(f"    - {error}")
                sys.exit(1)
            else:
                all_clients = config_manager.get_all_client_names()
                logger.info(f"All {len(all_clients)} client configurations are valid")

        logger.info("Configuration validation completed successfully")

    except ConfigurationException as e:
        logger.error(f"Configuration validation failed: {e.message}")
        sys.exit(1)
    except WorkMonitorException as e:
        logger.error(f"Configuration validation failed: {e.message}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during validation: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option('--show-config', is_flag=True, help='Show detailed configuration for each client')
@click.pass_context
def list_clients(ctx, show_config: bool):
    """List available clients."""
    logger = ctx.obj['logger']

    try:
        logger.info("Listing available clients")

        # Initialize configuration manager
        config_manager = ConfigManager()

        all_clients = config_manager.get_all_client_names()
        active_clients = config_manager.get_active_clients()

        if not all_clients:
            click.echo("No clients configured.")
            return

        click.echo(f"Found {len(all_clients)} configured clients:")
        click.echo()

        for client_name in all_clients:
            status = "ACTIVE" if client_name in active_clients else "INACTIVE"
            click.echo(f"  • {client_name} ({status})")

            if show_config:
                client_config = config_manager.get_client_config(client_name)
                if client_config:
                    click.echo(f"    Schedule: {client_config.schedule_cron}")
                    click.echo(f"    Platforms: {', '.join(client_config.supported_platforms)}")
                    click.echo(f"    Max tasks: {client_config.max_tasks_per_run}")

                    # Validate configuration
                    validation_errors = config_manager.validate_client_config(client_name)
                    if validation_errors:
                        click.echo(f"    ⚠️  Configuration issues: {len(validation_errors)}")
                    else:
                        click.echo(f"    ✅ Configuration valid")
                click.echo()

        click.echo(f"Active clients: {len(active_clients)}/{len(all_clients)}")

    except Exception as e:
        logger.error(f"Error listing clients: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('client_name')
@click.option('--output', help='Output path for the configuration file')
@click.pass_context
def create_config(ctx, client_name: str, output: Optional[str]):
    """Create a sample configuration file for a new client."""
    logger = ctx.obj['logger']

    try:
        logger.info(f"Creating sample configuration for client: {client_name}")

        # Initialize configuration manager
        config_manager = ConfigManager()

        # Check if client already exists
        if client_name in config_manager.get_all_client_names():
            click.echo(f"Warning: Client '{client_name}' already exists.")
            if not click.confirm("Do you want to overwrite the existing configuration?"):
                return

        # Create sample configuration
        config_path = config_manager.create_sample_config(client_name, output)

        click.echo(f"Sample configuration created: {config_path}")
        click.echo()
        click.echo("Please edit the configuration file and update the following:")
        click.echo("  - user_info.api-token")
        click.echo("  - user_info.user_id")
        click.echo("  - user_info.tenant")
        click.echo("  - bitable.webhook")
        click.echo("  - bitable.dentryUuid")
        click.echo("  - bitable.idOrName")
        click.echo()
        click.echo(f"After editing, validate with: python -m main validate --client {client_name}")

    except Exception as e:
        logger.error(f"Error creating configuration: {str(e)}")
        sys.exit(1)


def main():
    """Main entry point for the application."""
    cli()


if __name__ == '__main__':
    main()