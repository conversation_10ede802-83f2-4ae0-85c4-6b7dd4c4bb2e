"""
Configuration management for the work monitor system.
"""

import os
import yaml
import json
from typing import Dict, List, Optional, Any
from pathlib import Path

from core.context import ClientConfig
from core.exceptions import ConfigurationException


class ConfigManager:
    """
    Manages configuration loading and validation for clients.
    """
    
    def __init__(self, config_dir: str = "config/clients"):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir: Directory containing client configuration files
        """
        self.config_dir = Path(config_dir)
        self._client_configs: Dict[str, ClientConfig] = {}
        self._load_all_configs()
    
    def _load_all_configs(self) -> None:
        """Load all client configurations from the config directory."""
        if not self.config_dir.exists():
            raise ConfigurationException(f"Configuration directory not found: {self.config_dir}")
        
        for config_file in self.config_dir.glob("*.yaml"):
            try:
                client_name = config_file.stem
                config_data = self._load_config_file(config_file)
                client_config = ClientConfig.from_dict(config_data)
                client_config.name = client_name  # Ensure name matches filename
                self._client_configs[client_name] = client_config
            except Exception as e:
                raise ConfigurationException(f"Failed to load config for {config_file}: {str(e)}")
        
        # Also try JSON files
        for config_file in self.config_dir.glob("*.json"):
            try:
                client_name = config_file.stem
                if client_name not in self._client_configs:  # YAML takes precedence
                    config_data = self._load_config_file(config_file)
                    client_config = ClientConfig.from_dict(config_data)
                    client_config.name = client_name
                    self._client_configs[client_name] = client_config
            except Exception as e:
                raise ConfigurationException(f"Failed to load config for {config_file}: {str(e)}")
    
    def _load_config_file(self, config_file: Path) -> Dict[str, Any]:
        """
        Load configuration from a file.
        
        Args:
            config_file: Path to the configuration file
            
        Returns:
            Configuration dictionary
        """
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
                return yaml.safe_load(f)
            elif config_file.suffix.lower() == '.json':
                return json.load(f)
            else:
                raise ConfigurationException(f"Unsupported config file format: {config_file.suffix}")
    
    def get_client_config(self, client_name: str) -> Optional[ClientConfig]:
        """
        Get configuration for a specific client.
        
        Args:
            client_name: Name of the client
            
        Returns:
            ClientConfig instance or None if not found
        """
        return self._client_configs.get(client_name)
    
    def get_all_client_names(self) -> List[str]:
        """
        Get list of all configured client names.
        
        Returns:
            List of client names
        """
        return list(self._client_configs.keys())
    
    def get_active_clients(self) -> List[str]:
        """
        Get list of active client names.
        
        Returns:
            List of active client names
        """
        # For now, all loaded clients are considered active
        # This can be extended to check for an 'active' flag in config
        return self.get_all_client_names()
    
    def validate_client_config(self, client_name: str) -> List[str]:
        """
        Validate configuration for a specific client.
        
        Args:
            client_name: Name of the client to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        config = self.get_client_config(client_name)
        if not config:
            errors.append(f"Configuration not found for client: {client_name}")
            return errors
        
        # Validate required fields
        if not config.name:
            errors.append("Client name is required")
        
        if not config.user_info:
            errors.append("User info is required")
        else:
            if not config.user_info.get('api-token'):
                errors.append("API token is required in user_info")
            if not config.user_info.get('user_id'):
                errors.append("User ID is required in user_info")
        
        if not config.bitable_config:
            errors.append("Bitable configuration is required")
        
        # Validate schedule format (basic check)
        if config.schedule_cron and len(config.schedule_cron.split()) != 5:
            errors.append("Invalid cron format in schedule")
        
        # Validate platforms
        valid_platforms = ['xhs', 'dy', 'ks', 'wxvideo', 'tiktok']
        for platform in config.supported_platforms:
            if platform not in valid_platforms:
                errors.append(f"Unsupported platform: {platform}")
        
        return errors
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """
        Validate all client configurations.
        
        Returns:
            Dictionary mapping client names to their validation errors
        """
        validation_results = {}
        
        for client_name in self.get_all_client_names():
            errors = self.validate_client_config(client_name)
            if errors:
                validation_results[client_name] = errors
        
        return validation_results
    
    def reload_config(self, client_name: Optional[str] = None) -> None:
        """
        Reload configuration for a specific client or all clients.
        
        Args:
            client_name: Name of the client to reload, or None to reload all
        """
        if client_name:
            config_file = self.config_dir / f"{client_name}.yaml"
            if not config_file.exists():
                config_file = self.config_dir / f"{client_name}.json"
            
            if config_file.exists():
                config_data = self._load_config_file(config_file)
                client_config = ClientConfig.from_dict(config_data)
                client_config.name = client_name
                self._client_configs[client_name] = client_config
            else:
                raise ConfigurationException(f"Configuration file not found for client: {client_name}")
        else:
            self._client_configs.clear()
            self._load_all_configs()
    
    def create_sample_config(self, client_name: str, output_path: Optional[str] = None) -> str:
        """
        Create a sample configuration file for a client.
        
        Args:
            client_name: Name of the client
            output_path: Optional output path, defaults to config directory
            
        Returns:
            Path to the created sample config file
        """
        sample_config = {
            'name': client_name,
            'schedule': {
                'cron': '0 */2 * * *',
                'max_tasks': 100
            },
            'data_fetcher': {
                'source_type': 'dingtalk_bitable',
                'filter_rules': {
                    'update_times_limit': 5
                }
            },
            'work_updater': {
                'platforms': ['xhs', 'dy'],
                'batch_size': 50
            },
            'data_sync': {
                'field_mapping': {
                    '作品标题': 'title',
                    '点赞数': 'like_count',
                    '评论数': 'comment_count',
                    '分享数': 'share_count',
                    '收藏数': 'collect_count'
                },
                'batch_size': 100
            },
            'notification': {
                'threshold': {
                    'like_count': 50
                },
                'message_template': '作品阈值通知：标题={title}, 作者={author_name}',
                'channels': ['dingtalk']
            },
            'user_info': {
                'api-token': 'your-api-token-here',
                'user_id': 0,
                'tenant': 'your-tenant'
            },
            'bitable': {
                'webhook': 'your-webhook-url',
                'dentryUuid': 'your-dentry-uuid',
                'idOrName': 'your-table-name'
            }
        }
        
        if not output_path:
            output_path = self.config_dir / f"{client_name}.yaml"
        else:
            output_path = Path(output_path)
        
        # Ensure directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True)
        
        return str(output_path)
