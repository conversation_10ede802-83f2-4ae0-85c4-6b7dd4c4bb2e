# -*- coding: utf-8 -*-

from .models.comment_model import CommentModel
from ..client.qb.handler import Handler
from ..client.qb.models import Platform, Comment
from ..client.tikhub_xhs_client import TikhubXhsClient
from ..utils.string_utils import StringUtils
from ..utils.uuid_utils import UUIDUtils


class XhsCommentHandler:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.tikhub_xhs_client = TikhubXhsClient(logger_=logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    def query_article_comment(self, note_id, count) -> tuple[bool, None, None] | tuple[
        bool, list[CommentModel], list[CommentModel]]:
        try:
            all_comment_object = []
            comment_list = []

            # 获取所有一级评论
            work_url = "https://www.xiaohongshu.com/discovery/item/" + note_id

            first_level_comment_list = self._query_one_level_comments(work_url=work_url, count=count)
            if first_level_comment_list is None:
                return False, None, None

            all_comment_object.extend(first_level_comment_list)
            comment_list.extend(first_level_comment_list)
            if len(comment_list) >= count:
                return True, all_comment_object, comment_list

            for first_item in all_comment_object:
                if first_item.has_sub == 1:
                    sub_comments = self._query_sub_level_comments(first_item.work_url)
                    first_item.sub_comments.extend(sub_comments)
                    comment_list.extend(sub_comments)

                    if len(comment_list) >= count:
                        return True, all_comment_object, comment_list

            return True, all_comment_object, comment_list
        except Exception as e:
            self.logger_.error(e)
            return False, None, None

    def _query_one_level_comments(self, work_url, count, platform="xhs"):
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_comment(key=work_url, count=count)
            for comment in comment_list:
                work_comment_list.append(self._convert_qingbo_to_model(platform, comment))

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _query_sub_level_comments(self, work_url, platform="xhs") -> list | None:
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_sub_comment(key=work_url)
            for comment in comment_list:
                work_comment_list.append(self._convert_qingbo_to_model(platform, comment))

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _convert_qingbo_to_model(self, platform, item: Comment):
        try:
            commenter_id = item.comment_user_id
            comment_posttime = item.comment_posttime
            comment_content = item.comment_content

            # 安全处理数字字段，确保是整数类型
            def safe_int(value, default=0):
                if isinstance(value, str):
                    try:
                        return int(value) if value.strip() else default
                    except (ValueError, AttributeError):
                        return default
                return value if isinstance(value, int) else default

            like_count = safe_int(item.comment_like_count, 0)
            has_sub = safe_int(item.has_sub, 0)
            is_sub = safe_int(item.is_sub, 0)

            comment_uuid = UUIDUtils.generate_comment_uuid(commenter_id, comment_content, comment_posttime)
            return CommentModel(
                work_url=item.news_url,
                platform=platform,
                comment_uuid=comment_uuid,
                commenter_name=item.comment_user,
                commenter_id=commenter_id,
                commenter_url="https://www.xiaohongshu.com/user/profile/" + commenter_id,
                content=comment_content,
                publish_time=comment_posttime,
                like_count=like_count,
                location_ip=item.comment_ip_location,
                first_cid=item.first_cid,
                has_sub=has_sub,
                is_sub=is_sub,
                sec_uid=item.sec_uid,
                news_parent_id=item.news_parent_id
            )
        except Exception as e:
            self.logger_.error(f"_convert_qingbo_to_model {StringUtils.obj_2_json_string(item)}, {e}")

    def is_success(self, data):
        if data is None:
            return False
        if "data" not in data:
            return False
        return True
