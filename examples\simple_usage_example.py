#!/usr/bin/env python3
"""
简化的4个核心Handler使用示例

展示如何使用重构后的4个核心Handler：
1. DataHandler - 获取数据
2. UpdateHandler - 执行更新逻辑  
3. NotifyHandler - 执行通知逻辑
4. SyncHandler - 同步数据
"""

import logging
import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.context import ClientConfig
from core.handlers import (
    # 4个核心Handler
    UpdateHandler,
    DatabaseUpdateHandler,
    # 简化的工厂和构建器
    CoreHandlerFactory,
    create_data_handler,
    create_update_handler,
    create_notify_handler,
    create_sync_handler,
    build_chain
)


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('simple_usage_example')


def example_1_most_simple():
    """
    示例1：最简单的使用方式
    """
    print("=" * 60)
    print("示例1：最简单的使用方式")
    print("=" * 60)

    logger = setup_logging()

    # 1. 创建配置
    config_data = {
        'name': 'simple-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    }
    config = ClientConfig.from_dict(config_data)

    # 2. 一行代码创建完整的处理链（明确指定数据源）
    chain = build_chain(config, logger, data_source='bitable', chain_type='standard')

    print("✅ 一行代码创建完整处理链")
    print("   包含：数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步")

    return chain


def example_2_direct_inheritance():
    """
    示例2：直接继承自定义Handler（推荐）
    """
    print("\n" + "=" * 60)
    print("示例2：直接继承自定义Handler")
    print("=" * 60)

    logger = setup_logging()

    # 1. 继承核心Handler，添加自定义逻辑
    class MyCustomUpdateHandler(UpdateHandler):
        """我的自定义更新Handler"""

        def _process_single_task(self, task, context):
            self.logger.info(f"MyCustom processing: {task.work_url}")

            # 添加自定义前处理
            if not self._validate_my_rules(task):
                task.error_message = "Failed my custom validation"
                return task

            # 调用父类方法
            result = super()._process_single_task(task, context)

            # 添加自定义后处理
            if result.author_work:
                self._add_my_metadata(result)

            return result

        def _validate_my_rules(self, task):
            """我的自定义验证规则"""
            # 示例：只处理小红书和抖音
            return task.platform in ['xhs', 'dy']

        def _add_my_metadata(self, task):
            """添加我的自定义元数据"""
            if not task.metadata:
                task.metadata = {}
            task.metadata['my_custom_processed'] = True
            task.metadata['my_score'] = self._calculate_my_score(task.author_work)

        def _calculate_my_score(self, work):
            """我的自定义评分算法"""
            like_count = getattr(work, 'like_count', 0)
            comment_count = getattr(work, 'comment_count', 0)
            return (like_count * 0.3) + (comment_count * 1.5)

    # 2. 创建配置
    config_data = {
        'name': 'my-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    }
    config = ClientConfig.from_dict(config_data)

    # 3. 组合自定义Handler
    data_handler = create_data_handler(config, logger, "bitable")  # 明确指定数据源
    update_handler = MyCustomUpdateHandler(config, logger)  # 使用自定义Handler
    notify_handler = create_notify_handler(config, logger)
    sync_handler = create_sync_handler(config, logger)

    # 4. 构建链
    chain = data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)

    print("✅ 直接继承创建自定义Handler")
    print(f"   自定义更新Handler: {update_handler.__class__.__name__}")
    print("   包含自定义验证和评分逻辑")

    return chain


def example_3_factory_usage():
    """
    示例3：使用工厂精确控制
    """
    print("\n" + "=" * 60)
    print("示例3：使用工厂精确控制")
    print("=" * 60)

    logger = setup_logging()

    # 1. 创建配置
    config_data = {
        'name': 'factory-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table', 'webhook': 'https://test.com'}
    }
    config = ClientConfig.from_dict(config_data)

    # 2. 使用工厂精确指定每个Handler类型
    data_handler = CoreHandlerFactory.create_dingtalk_data_handler(config, logger)
    update_handler = CoreHandlerFactory.create_database_update_handler(config, logger)  # 数据库版本
    notify_handler = CoreHandlerFactory.create_dingtalk_notify_handler(config, logger)
    sync_handler = CoreHandlerFactory.create_webhook_sync_handler(config, logger)  # Webhook版本

    # 3. 构建链
    chain = data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)

    print("✅ 使用工厂精确控制Handler类型")
    print(f"   数据获取: {data_handler.__class__.__name__}")
    print(f"   更新逻辑: {update_handler.__class__.__name__}")
    print(f"   通知逻辑: {notify_handler.__class__.__name__}")
    print(f"   数据同步: {sync_handler.__class__.__name__}")

    return chain


def example_4_custom_chain_builder():
    """
    示例4：自定义链构建器
    """
    print("\n" + "=" * 60)
    print("示例4：自定义链构建器")
    print("=" * 60)

    logger = setup_logging()

    # 1. 定义自定义链构建器
    class MyChainBuilder:
        """我的自定义链构建器"""

        @staticmethod
        def build_production_chain(config, logger):
            """生产环境链"""

            # 自定义Handler
            class ProductionUpdateHandler(DatabaseUpdateHandler):
                def _process_single_task(self, task, context):
                    self.logger.info("Production processing with database")
                    return super()._process_single_task(task, context)

            # 组合链
            data_handler = CoreHandlerFactory.create_dingtalk_data_handler(config, logger)
            update_handler = ProductionUpdateHandler(config, logger)
            notify_handler = CoreHandlerFactory.create_dingtalk_notify_handler(config, logger)
            sync_handler = CoreHandlerFactory.create_dingtalk_sync_handler(config, logger)

            return data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)

        @staticmethod
        def build_testing_chain(config, logger):
            """测试环境链"""
            data_handler = CoreHandlerFactory.create_dingtalk_data_handler(config, logger)
            update_handler = CoreHandlerFactory.create_update_handler(config, logger)  # 基础版本
            sync_handler = CoreHandlerFactory.create_webhook_sync_handler(config, logger)
            # 测试环境不发通知

            return data_handler.set_next(update_handler).set_next(sync_handler)

    # 2. 创建配置
    config_data = {
        'name': 'my-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    }
    config = ClientConfig.from_dict(config_data)

    # 3. 使用自定义构建器
    production_chain = MyChainBuilder.build_production_chain(config, logger)
    testing_chain = MyChainBuilder.build_testing_chain(config, logger)

    print("✅ 自定义链构建器")
    print("   生产环境: 数据获取 -> 数据库更新 -> 钉钉通知 -> 钉钉同步")
    print("   测试环境: 数据获取 -> 基础更新 -> Webhook同步")

    return production_chain


def example_5_different_chain_types():
    """
    示例5：不同类型的处理链
    """
    print("\n" + "=" * 60)
    print("示例5：不同类型的处理链")
    print("=" * 60)

    logger = setup_logging()

    # 创建配置
    config_data = {
        'name': 'multi-client',
        'user_info': {'api-token': 'test-token'},
        'bitable': {'dentryUuid': 'test-uuid', 'idOrName': 'test-table'}
    }
    config = ClientConfig.from_dict(config_data)

    # 1. 标准链（包含所有4个Handler）
    standard_chain = build_chain(config, logger, 'standard')
    print("✅ 标准链: 数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步")

    # 2. 基础链（不包含通知）
    basic_chain = build_chain(config, logger, 'basic')
    print("✅ 基础链: 数据获取 -> 更新逻辑 -> 数据同步")

    # 3. 监控链（不包含同步）
    monitoring_chain = build_chain(config, logger, 'monitoring')
    print("✅ 监控链: 数据获取 -> 更新逻辑 -> 通知逻辑")

    # 4. 自定义链（只有数据获取和更新）
    data_handler = create_data_handler(config, logger)
    update_handler = create_update_handler(config, logger)
    custom_chain = data_handler.set_next(update_handler)
    print("✅ 自定义链: 数据获取 -> 更新逻辑")

    return {
        'standard': standard_chain,
        'basic': basic_chain,
        'monitoring': monitoring_chain,
        'custom': custom_chain
    }


def compare_old_vs_new():
    """
    对比旧方式 vs 新方式
    """
    print("\n" + "=" * 60)
    print("对比：旧方式 vs 新方式")
    print("=" * 60)

    print("🔴 旧方式（复杂）:")
    print("   - 多个复杂的工厂类")
    print("   - 复杂的注册机制")
    print("   - 不直观的Handler名称")
    print("   - 需要理解工厂模式")
    print()
    print("   # 旧方式代码")
    print("   from core.handlers import WorkUpdaterHandler, create_work_updater")
    print("   work_updater = create_work_updater(config, logger, 'database')")
    print()

    print("🟢 新方式（简单）:")
    print("   - 4个清晰的核心Handler")
    print("   - 直接继承，无需工厂")
    print("   - 直观的Handler名称")
    print("   - 简单的链构建")
    print()
    print("   # 新方式代码")
    print("   from core.handlers import UpdateHandler, create_update_handler")
    print("   update_handler = create_update_handler(config, logger, use_database=True)")
    print("   # 或者直接继承")
    print("   class MyUpdateHandler(UpdateHandler): pass")
    print("   update_handler = MyUpdateHandler(config, logger)")
    print()

    print("✅ 新方式的优势:")
    print("   - 🎯 清晰：4个核心Handler，职责明确")
    print("   - 🔧 简单：直接继承，无需复杂配置")
    print("   - 📋 直观：Handler名称一目了然")
    print("   - 🚀 灵活：自由组合，满足各种需求")
    print("   - 🛡️ 安全：客户代码完全独立")


def main():
    """Main function demonstrating all examples."""
    print("🚀 简化的4个核心Handler使用示例")

    try:
        # 运行所有示例
        example_1_most_simple()
        example_2_direct_inheritance()
        example_3_factory_usage()
        example_4_custom_chain_builder()
        example_5_different_chain_types()
        compare_old_vs_new()

        print("\n" + "=" * 60)
        print("🎉 所有示例运行成功！")
        print("=" * 60)
        print()
        print("📚 推荐的使用方式:")
        print("   1. 最简单：build_chain(config, logger, 'standard')")
        print("   2. 自定义：直接继承Handler类")
        print("   3. 精确控制：使用CoreHandlerFactory")
        print("   4. 复杂需求：创建自定义链构建器")
        print()
        print("🔧 4个核心Handler:")
        print("   1. DataHandler - 获取数据")
        print("   2. UpdateHandler - 执行更新逻辑")
        print("   3. NotifyHandler - 执行通知逻辑")
        print("   4. SyncHandler - 同步数据")

    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return False

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
