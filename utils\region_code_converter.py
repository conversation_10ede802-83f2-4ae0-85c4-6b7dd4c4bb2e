# -*- encoding:utf-8 -*-

import json
import os


class RegionCodeConverter:
    def __init__(self, data_path=None):
        """初始化行政区划转换器，加载离线数据"""
        self.data = self._load_data(data_path)

    def _load_data(self, data_path):
        """加载行政区划数据"""
        # 如果提供了外部数据文件路径
        if data_path and os.path.exists(data_path):
            try:
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass  # 如果外部文件加载失败，使用内置数据

        # 内置行政区划数据（截至2023年12月31日）
        return {
            "provinces": {
                "11": "北京市", "12": "天津市", "13": "河北省", "14": "山西省", "15": "内蒙古自治区",
                "21": "辽宁省", "22": "吉林省", "23": "黑龙江省", "31": "上海市", "32": "江苏省",
                "33": "浙江省", "34": "安徽省", "35": "福建省", "36": "江西省", "37": "山东省",
                "41": "河南省", "42": "湖北省", "43": "湖南省", "44": "广东省", "45": "广西壮族自治区",
                "46": "海南省", "50": "重庆市", "51": "四川省", "52": "贵州省", "53": "云南省",
                "54": "西藏自治区", "61": "陕西省", "62": "甘肃省", "63": "青海省", "64": "宁夏回族自治区",
                "65": "新疆维吾尔自治区", "71": "台湾省", "81": "香港特别行政区", "82": "澳门特别行政区"
            },
            "cities": {
                # 北京市
                "1101": "北京市",
                # 天津市
                "1201": "天津市",
                # 河北省
                "1301": "石家庄市", "1302": "唐山市", "1303": "秦皇岛市", "1304": "邯郸市", "1305": "邢台市",
                "1306": "保定市", "1307": "张家口市", "1308": "承德市", "1309": "沧州市", "1310": "廊坊市", "1311": "衡水市",
                # 山西省
                "1401": "太原市", "1402": "大同市", "1403": "阳泉市", "1404": "长治市", "1405": "晋城市",
                "1406": "朔州市", "1407": "晋中市", "1408": "运城市", "1409": "忻州市", "1410": "临汾市", "1411": "吕梁市",
                # 内蒙古自治区
                "1501": "呼和浩特市", "1502": "包头市", "1503": "乌海市", "1504": "赤峰市", "1505": "通辽市",
                "1506": "鄂尔多斯市", "1507": "呼伦贝尔市", "1508": "巴彦淖尔市", "1509": "乌兰察布市", "1522": "兴安盟",
                "1525": "锡林郭勒盟", "1529": "阿拉善盟",
                # 辽宁省
                "2101": "沈阳市", "2102": "大连市", "2103": "鞍山市", "2104": "抚顺市", "2105": "本溪市",
                "2106": "丹东市", "2107": "锦州市", "2108": "营口市", "2109": "阜新市", "2110": "辽阳市",
                "2111": "盘锦市", "2112": "铁岭市", "2113": "朝阳市", "2114": "葫芦岛市",
                # 吉林省
                "2201": "长春市", "2202": "吉林市", "2203": "四平市", "2204": "辽源市", "2205": "通化市",
                "2206": "白山市", "2207": "松原市", "2208": "白城市", "2224": "延边朝鲜族自治州",
                # 黑龙江省
                "2301": "哈尔滨市", "2302": "齐齐哈尔市", "2303": "鸡西市", "2304": "鹤岗市", "2305": "双鸭山市",
                "2306": "大庆市", "2307": "伊春市", "2308": "佳木斯市", "2309": "七台河市", "2310": "牡丹江市",
                "2311": "黑河市", "2312": "绥化市", "2327": "大兴安岭地区",
                # 上海市
                "3101": "上海市",
                # 江苏省
                "3201": "南京市", "3202": "无锡市", "3203": "徐州市", "3204": "常州市", "3205": "苏州市",
                "3206": "南通市", "3207": "连云港市", "3208": "淮安市", "3209": "盐城市", "3210": "扬州市",
                "3211": "镇江市", "3212": "泰州市", "3213": "宿迁市",
                # 浙江省
                "3301": "杭州市", "3302": "宁波市", "3303": "温州市", "3304": "嘉兴市", "3305": "湖州市",
                "3306": "绍兴市", "3307": "金华市", "3308": "衢州市", "3309": "舟山市", "3310": "台州市", "3311": "丽水市",
                # 安徽省
                "3401": "合肥市", "3402": "芜湖市", "3403": "蚌埠市", "3404": "淮南市", "3405": "马鞍山市",
                "3406": "淮北市", "3407": "铜陵市", "3408": "安庆市", "3410": "黄山市", "3411": "滁州市",
                "3412": "阜阳市", "3413": "宿州市", "3415": "六安市", "3416": "亳州市", "3417": "池州市", "3418": "宣城市",
                # 福建省
                "3501": "福州市", "3502": "厦门市", "3503": "莆田市", "3504": "三明市", "3505": "泉州市",
                "3506": "漳州市", "3507": "南平市", "3508": "龙岩市", "3509": "宁德市",
                # 江西省
                "3601": "南昌市", "3602": "景德镇市", "3603": "萍乡市", "3604": "九江市", "3605": "新余市",
                "3606": "鹰潭市", "3607": "赣州市", "3608": "吉安市", "3609": "宜春市", "3610": "抚州市", "3611": "上饶市",
                # 山东省
                "3701": "济南市", "3702": "青岛市", "3703": "淄博市", "3704": "枣庄市", "3705": "东营市",
                "3706": "烟台市", "3707": "潍坊市", "3708": "济宁市", "3709": "泰安市", "3710": "威海市",
                "3711": "日照市", "3713": "临沂市", "3714": "德州市", "3715": "聊城市", "3716": "滨州市", "3717": "菏泽市",
                # 河南省
                "4101": "郑州市", "4102": "开封市", "4103": "洛阳市", "4104": "平顶山市", "4105": "安阳市",
                "4106": "鹤壁市", "4107": "新乡市", "4108": "焦作市", "4109": "濮阳市", "4110": "许昌市",
                "4111": "漯河市", "4112": "三门峡市", "4113": "南阳市", "4114": "商丘市", "4115": "信阳市", "4116": "周口市",
                "4117": "驻马店市",
                # 湖北省
                "4201": "武汉市", "4202": "黄石市", "4203": "十堰市", "4205": "宜昌市", "4206": "襄阳市",
                "4207": "鄂州市", "4208": "荆门市", "4209": "孝感市", "4210": "荆州市", "4211": "黄冈市",
                "4212": "咸宁市", "4213": "随州市", "4228": "恩施土家族苗族自治州",
                # 湖南省
                "4301": "长沙市", "4302": "株洲市", "4303": "湘潭市", "4304": "衡阳市", "4305": "邵阳市",
                "4306": "岳阳市", "4307": "常德市", "4308": "张家界市", "4309": "益阳市", "4310": "郴州市",
                "4311": "永州市", "4312": "怀化市", "4313": "娄底市", "4331": "湘西土家族苗族自治州",
                # 广东省
                "4401": "广州市", "4402": "韶关市", "4403": "深圳市", "4404": "珠海市", "4405": "汕头市",
                "4406": "佛山市", "4407": "江门市", "4408": "湛江市", "4409": "茂名市", "4412": "肇庆市",
                "4413": "惠州市", "4414": "梅州市", "4415": "汕尾市", "4416": "河源市", "4417": "阳江市",
                "4418": "清远市", "4419": "东莞市", "4420": "中山市", "4451": "潮州市", "4452": "揭阳市", "4453": "云浮市",
                # 广西壮族自治区
                "4501": "南宁市", "4502": "柳州市", "4503": "桂林市", "4504": "梧州市", "4505": "北海市",
                "4506": "防城港市", "4507": "钦州市", "4508": "贵港市", "4509": "玉林市", "4510": "百色市",
                "4511": "贺州市", "4512": "河池市", "4513": "来宾市", "4514": "崇左市",
                # 海南省
                "4601": "海口市", "4602": "三亚市", "4603": "三沙市", "4604": "儋州市",
                # 重庆市
                "5001": "重庆市",
                # 四川省
                "5101": "成都市", "5103": "自贡市", "5104": "攀枝花市", "5105": "泸州市", "5106": "德阳市",
                "5107": "绵阳市", "5108": "广元市", "5109": "遂宁市", "5110": "内江市", "5111": "乐山市",
                "5113": "南充市", "5114": "眉山市", "5115": "宜宾市", "5116": "广安市", "5117": "达州市",
                "5118": "雅安市", "5119": "巴中市", "5120": "资阳市", "5132": "阿坝藏族羌族自治州",
                "5133": "甘孜藏族自治州", "5134": "凉山彝族自治州",
                # 贵州省
                "5201": "贵阳市", "5202": "六盘水市", "5203": "遵义市", "5204": "安顺市", "5205": "毕节市",
                "5206": "铜仁市", "5223": "黔西南布依族苗族自治州", "5226": "黔东南苗族侗族自治州", "5227": "黔南布依族苗族自治州",
                # 云南省
                "5301": "昆明市", "5303": "曲靖市", "5304": "玉溪市", "5305": "保山市", "5306": "昭通市",
                "5307": "丽江市", "5308": "普洱市", "5309": "临沧市", "5323": "楚雄彝族自治州", "5325": "红河哈尼族彝族自治州",
                "5326": "文山壮族苗族自治州", "5328": "西双版纳傣族自治州", "5329": "大理白族自治州",
                "5331": "德宏傣族景颇族自治州", "5333": "怒江傈僳族自治州", "5334": "迪庆藏族自治州",
                # 西藏自治区
                "5401": "拉萨市", "5402": "日喀则市", "5403": "昌都市", "5404": "林芝市", "5405": "山南市", "5406": "那曲市",
                "5425": "阿里地区",
                # 陕西省
                "6101": "西安市", "6102": "铜川市", "6103": "宝鸡市", "6104": "咸阳市", "6105": "渭南市",
                "6106": "延安市", "6107": "汉中市", "6108": "榆林市", "6109": "安康市", "6110": "商洛市",
                # 甘肃省
                "6201": "兰州市", "6202": "嘉峪关市", "6203": "金昌市", "6204": "白银市", "6205": "天水市",
                "6206": "武威市", "6207": "张掖市", "6208": "平凉市", "6209": "酒泉市", "6210": "庆阳市",
                "6211": "定西市", "6212": "陇南市", "6229": "临夏回族自治州", "6230": "甘南藏族自治州",
                # 青海省
                "6301": "西宁市", "6302": "海东市", "6322": "海北藏族自治州", "6323": "黄南藏族自治州",
                "6325": "海南藏族自治州", "6326": "果洛藏族自治州", "6327": "玉树藏族自治州", "6328": "海西蒙古族藏族自治州",
                # 宁夏回族自治区
                "6401": "银川市", "6402": "石嘴山市", "6403": "吴忠市", "6404": "固原市", "6405": "中卫市",
                # 新疆维吾尔自治区
                "6501": "乌鲁木齐市", "6502": "克拉玛依市", "6504": "吐鲁番市", "6505": "哈密市",
                "6523": "昌吉回族自治州", "6527": "博尔塔拉蒙古自治州", "6528": "巴音郭楞蒙古自治州",
                "6529": "阿克苏地区", "6530": "克孜勒苏柯尔克孜自治州", "6531": "喀什地区",
                "6532": "和田地区", "6540": "伊犁哈萨克自治州", "6542": "塔城地区", "6543": "阿勒泰地区",
                # 台湾省（参考数据）
                "7101": "台北市", "7102": "高雄市", "7103": "基隆市", "7104": "台中市", "7105": "台南市",
                "7106": "新竹市", "7107": "嘉义市", "7108": "新北市", "7109": "桃园市", "7110": "新竹县",
                "7111": "苗栗县", "7112": "彰化县", "7113": "南投县", "7114": "云林县", "7115": "嘉义县",
                "7116": "屏东县", "7117": "宜兰县", "7118": "花莲县", "7119": "台东县", "7120": "澎湖县",
                # 香港特别行政区
                "8101": "香港特别行政区",
                # 澳门特别行政区
                "8201": "澳门特别行政区"
            },
            "districts": {
                # 示例：杭州市部分区县（完整数据需补充）
                "330102": "上城区", "330103": "下城区", "330104": "江干区", "330105": "拱墅区",
                "330106": "西湖区", "330108": "滨江区", "330109": "萧山区", "330110": "余杭区",
                "330111": "富阳区", "330112": "临安区", "330122": "桐庐县", "330127": "淳安县",
                "330182": "建德市",
                # 其他区县数据应在此补充...
            }
        }

    def convert(self, code):
        """
        将行政区划代码转换为行政区划名称

        参数:
            code: 行政区划代码 (str/int), 支持2位(省), 4位(市), 6位(区县)

        返回:
            dict: 包含省、市、区县名称的字典
        """
        if not isinstance(code, str):
            code = str(code).strip()

        # 处理特殊代码格式
        if len(code) == 2:
            code += "0000"
        elif len(code) == 4:
            code += "00"
        elif len(code) != 6:
            return {"error": "无效代码格式（需2/4/6位数字）"}

        result = {"province": "", "city": "", "district": "", "full_code": code}

        # 省级解析
        province_code = code[0:2]
        result["province"] = self.data["provinces"].get(province_code, "")

        # 市级解析
        city_code = code[0:4]
        result["city"] = self.data["cities"].get(city_code, "")

        # 区县级解析
        result["district"] = self.data["districts"].get(code, "")

        # 特殊处理：直辖市
        if province_code in ["11", "12", "31", "50"]:
            # 直辖市没有市级名称，省级名称即市级名称
            if not result["city"]:
                result["city"] = result["province"]

        return result

    def export_data(self, file_path):
        """导出行政区划数据到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False


# 使用示例
if __name__ == "__main__":
    # 创建转换器实例
    converter = RegionCodeConverter()

    # 示例转换
    print(converter.convert(330100))  # 杭州市
    print(converter.convert("110105"))  # 北京市朝阳区
    print(converter.convert(44))  # 广东省
    print(converter.convert("500000"))  # 重庆市

    # 导出数据到文件（用于后续加载）
    # converter.export_data("china_regions.json")

    # 从外部文件加载数据（如果文件存在）
    # converter_external = RegionCodeConverter("china_regions.json")