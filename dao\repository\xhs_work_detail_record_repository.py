# -*- coding: utf-8 -*-

from dao.db_adapter import g_database_product
from dao.model.author_work import AuthorWork
from utils.time_utils import TimeUtils


class XhsWorkDetailRecordRepository(object):
    """
    对应表格 bardata 操作
    """

    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, author_work_record: AuthorWork):
        sql = '''
            INSERT INTO `xhs_work_detail_record`(
                `author_id`, `author_identity`, `author_avatar`, 
                `author_name`, `author_url`, `work_id`, `work_uuid`, `url`, 
                `download_url`, `long_url`, `digest`, `title`, `thumbnail_link`, 
                `content`, `img_urls`, `video_urls`, `music_url`, `music_author_name`, 
                `music_id`, `music_name`, `publish_time`, `publish_day`, `location_ip`, 
                `read_count`, `like_count`, `comment_count`, `share_count`, `collect_count`, 
                `text_polarity`, `record_time`, `is_detail`, `is_del`) 
            VALUES (%s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, 
                %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s);'''

        args = (author_work_record.author_id, author_work_record.author_identity, author_work_record.author_avatar,
                author_work_record.author_name, author_work_record.author_url, author_work_record.work_id,
                author_work_record.work_uuid, author_work_record.url, author_work_record.download_url,
                author_work_record.long_url, author_work_record.digest, author_work_record.title,
                author_work_record.thumbnail_link, author_work_record.content, author_work_record.img_urls,
                author_work_record.video_urls, author_work_record.music_url, author_work_record.music_author_name,
                author_work_record.music_id, author_work_record.music_name, author_work_record.publish_time,
                author_work_record.publish_day, author_work_record.location_ip, author_work_record.read_count,
                author_work_record.like_count, author_work_record.comment_count, author_work_record.share_count,
                author_work_record.collect_count, author_work_record.text_polarity, author_work_record.record_time,
                author_work_record.is_detail, 0)

        return self.gdb_datasource.insert(sql=sql, args=args)

    def query_work_detail(self, work_id):
        sql = 'select * from xhs_work_detail_record where work_id=%s'
        return self.gdb_datasource.fetch_all(sql, (work_id))

    def update_work_detail(self, content, read_count, like_count,
                           comment_count, share_count,
                           collect_count, work_id):
        sql = 'update xhs_work_detail_record ' \
              'set `content`=%s, `read_count`=%s, `like_count`=%s, ' \
              '`comment_count`=%s, `share_count`=%s, `collect_count`=%s ' \
              'where work_id=%s'
        return self.gdb_datasource.execute(sql, (content, read_count, like_count,
                                                 comment_count, share_count,
                                                 collect_count, work_id))

    def update_work_detail_status(self, content, read_count, like_count,
                                  comment_count, share_count,
                                  collect_count, work_id, is_detail):
        sql = 'update xhs_work_detail_record ' \
              'set `content`=%s, `read_count`=%s, `like_count`=%s, ' \
              '`comment_count`=%s, `share_count`=%s, `collect_count`=%s , `is_detail`=%s ' \
              'where work_id=%s'
        return self.gdb_datasource.execute(sql, (content, read_count, like_count,
                                                 comment_count, share_count,
                                                 collect_count, is_detail, work_id))

    @staticmethod
    def build_author_work_record_entity(_row):
        return AuthorWork(
            _id=_row['id'],
            platform='小红书',
            author_id=_row['author_id'],
            author_identity=_row['author_identity'],
            author_avatar=_row['author_avatar'],
            author_name=_row['author_name'],
            author_url=_row['author_url'],
            work_id=_row['work_id'],
            work_uuid=_row['work_uuid'],
            url=_row['url'],
            download_url=_row['download_url'],
            long_url=_row['long_url'],
            digest=_row['digest'],
            title=_row['title'],
            thumbnail_link=_row['thumbnail_link'],
            content=_row['content'],
            img_urls=_row['img_urls'],
            video_urls=_row['video_urls'],
            music_url=_row['music_url'],
            music_author_name=_row['music_author_name'],
            music_id=_row['music_id'],
            music_name=_row['music_name'],
            publish_time=TimeUtils.dt_to_str(_row['publish_time']),
            publish_day=_row['publish_day'],
            location_ip=_row['location_ip'],
            read_count=_row['read_count'],
            like_count=_row['like_count'],
            comment_count=_row['comment_count'],
            share_count=_row['share_count'],
            collect_count=_row['collect_count'],
            text_polarity=_row['text_polarity'],
            record_time=_row['record_time'],
            is_detail=_row['is_detail']
        )
