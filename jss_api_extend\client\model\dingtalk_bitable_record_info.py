# -*- coding: utf-8 -*-

"""
{
'createdBy': {'unionId': 'C9KfMJuvGAysoLhyLXfGuwiEiE'},
'createdTime': 1752129560000,
'fields': {'标题': '大学生扬州旅游打卡美食攻略～', '更新时间': 1752128501000, '作者主页': 'https://www.xiaohongshu.com/user/profile/6191fce8000000001000df73'},
'id': '1LYFcH8Wjg',
'lastModifiedBy': {'unionId': 'C9KfMJuvGAysoLhyLXfGuwiEiE'},
'lastModifiedTime': 1752129560000
}
"""

# class DingtalkBitableRecordBaseInfo:
#     def __init__(self,
#                  id,
#                  create_by,
#                  created_time,
#                  last_modified_time,
#                  last_modified_by):
#         self.dentry_uuid = dentry_uuid



class DingtalkBitableRecordInfo:

    def __init__(self,
                 work_url,
                 update_count: int,
                 user_union_id,
                 row_id,
                 extends=""):
        self.work_url = work_url
        self.update_count = update_count
        self.row_id = row_id
        self.user_union_id = user_union_id,
        self.extends = extends


class DingTalkBitableDMRecord:
    """
        钉钉 大毛
    """
    def __init__(self,
                 work_url,
                 create_time,
                 row_id):
        self.work_url = work_url
        self.row_id = row_id
        self.create_time = create_time


class DMCommentRecords:
    """
        钉钉 大毛评论数据
    """
    def __init__(self,
                 work_url,
                 comment_uuid,
                 create_time,
                 row_id):
        self.work_url = work_url
        self.comment_uuid = comment_uuid
        self.row_id = row_id
        self.create_time = create_time
