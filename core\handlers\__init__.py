"""
Handler module for the work monitor system.

This module contains 4 core handlers for the chain of responsibility pattern:
1. DataHandler - 获取数据
2. UpdateHandler - 执行更新逻辑
3. NotifyHandler - 执行通知逻辑
4. SyncHandler - 同步数据
"""

# 4个核心Handler - 客户应该继承这些类
from .base_handler import BaseHandler
from .data_handler import DataHandler, ServerDataHandler, BitableDataHandler, DingTalkDataHandler
from .update_handler import UpdateHandler, DatabaseUpdateHandler
from .notify_handler import NotifyHandler, DingTalkNotifyHandler
from .sync_handler import SyncHandler, DingTalkSyncHandler, WebhookSyncHandler

# Pipeline上下文
from ..pipeline_context import PipelineContext

# 简化的工厂和便利函数
from .simple_factory import (
    CoreHandlerFactory,
    create_data_handler,
    create_server_data_handler,
    create_bitable_data_handler,
    create_update_handler,
    create_notify_handler,
    create_sync_handler,
    create_pipeline_context,
    Default<PERSON>hainBuilder,
    build_chain
)


__all__ = [
    # 核心Handler - 客户继承这些
    'BaseHandler',
    'DataHandler',
    'ServerDataHandler',
    'BitableDataHandler',
    'DingTalkDataHandler',
    'UpdateHandler',
    'DatabaseUpdateHandler',
    'NotifyHandler',
    'DingTalkNotifyHandler',
    'SyncHandler',
    'DingTalkSyncHandler',
    'WebhookSyncHandler',

    # Pipeline上下文
    'PipelineContext',

    # 工厂和构建器
    'CoreHandlerFactory',
    'DefaultChainBuilder',
    'create_data_handler',
    'create_server_data_handler',
    'create_bitable_data_handler',
    'create_update_handler',
    'create_notify_handler',
    'create_sync_handler',
    'create_pipeline_context',
    'build_chain'
]
