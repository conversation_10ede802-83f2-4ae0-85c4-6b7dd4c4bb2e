# -*- coding: utf-8 -*-

from .models.comment_model import CommentModel
from ..client.jzl_wxvideo_cllient import JZLWxVideoClient
from ..utils.string_utils import StringUtils
from ..utils.uuid_utils import UUIDUtils


class WxVideoCommentHandler:
    def __init__(self, logger_, api_key="JZLf2b73896ba1b9a8e"):
        """
        初始化微信视频评论处理器

        :param logger_: 日志记录器
        :param api_key: 极致了API密钥
        """
        self.logger_ = logger_
        self.jzl_client = JZLWxVideoClient(key=api_key)

    def query_video_comments(
        self, object_id, count, object_nonce_id=None
    ) -> tuple[bool, None, None] | tuple[bool, list[CommentModel], list[CommentModel]]:
        """
        获取微信视频号评论

        :param object_id: 视频唯一ID
        :param count: 需要获取的评论数量
        :param object_nonce_id: 可选的nonce_id参数
        :return: (是否成功, 所有评论对象列表, 评论列表)
        """
        try:
            all_comment_object = []
            comment_list = []
            last_buffer = None

            while len(comment_list) < count:
                # 调用JZL接口获取评论
                response_data, failed = self.jzl_client.get_video_comments(
                    object_id=object_id,
                    object_nonce_id=object_nonce_id,
                    last_buffer=last_buffer,
                )

                if failed or response_data is None:
                    self.logger_.error(f"获取微信视频评论失败: object_id={object_id}")
                    return False, None, None

                # 检查响应状态
                if not self._is_success(response_data):
                    self.logger_.error(f"API返回错误: {response_data}")
                    return False, None, None

                # 解析评论数据
                comment_info = response_data.get("comment_info", [])
                if not comment_info:
                    break

                # 转换为CommentModel对象
                for comment_data in comment_info:
                    comment_model = self._convert_jzl_to_model(comment_data, object_id)
                    if comment_model:
                        all_comment_object.append(comment_model)
                        comment_list.append(comment_model)

                        # 如果达到所需数量，停止获取
                        if len(comment_list) >= count:
                            break

                # 检查是否还有更多数据
                last_buffer = response_data.get("last_buffer", "")
                down_continue_flag = response_data.get("down_continue_flag", 0)

                if not last_buffer or down_continue_flag == 0:
                    break

                if len(comment_list) >= count:
                    break

            return True, all_comment_object, comment_list

        except Exception as e:
            self.logger_.error(f"query_video_comments异常: {e}")
            return False, None, None

    def _convert_jzl_to_model(self, comment_data, object_id):
        """
        将JZL API返回的评论数据转换为CommentModel

        :param comment_data: JZL API返回的评论数据
        :param object_id: 视频ID
        :return: CommentModel对象
        """
        try:
            # 提取评论基本信息
            username = comment_data.get("username", "")
            nickname = comment_data.get("nickname", "")
            content = comment_data.get("content", "")
            comment_id = comment_data.get("comment_id", "")
            createtime = comment_data.get("createtime", "")
            like_count = comment_data.get("like_count", 0)

            # 获取IP地区信息
            ip_region_info = comment_data.get("ip_region_info", {})
            location_ip = (
                ip_region_info.get("region_text", "") if ip_region_info else ""
            )

            # 安全处理数字字段
            def safe_int(value, default=0):
                if isinstance(value, str):
                    try:
                        return int(value) if value.strip() else default
                    except (ValueError, AttributeError):
                        return default
                return value if isinstance(value, int) else default

            like_count = safe_int(like_count, 0)
            expand_comment_count = safe_int(
                comment_data.get("expand_comment_count", 0), 0
            )
            has_sub = 1 if expand_comment_count > 0 else 0

            # 生成评论UUID
            comment_uuid = UUIDUtils.generate_comment_uuid(
                username, content, createtime
            )

            # work_url 为 视频唯一ID
            work_url = object_id
            commenter_url = ""

            return CommentModel(
                work_url=work_url,
                platform="wxvideo",
                comment_uuid=comment_uuid,
                commenter_name=nickname,
                commenter_id=username,
                commenter_url=commenter_url,
                content=content,
                publish_time=createtime,
                like_count=like_count,
                location_ip=location_ip,
                first_cid=comment_id,
                has_sub=has_sub,
                is_sub=0,
                sec_uid="",
                news_parent_id="",
            )

        except Exception as e:
            self.logger_.error(
                f"_convert_jzl_to_model 转换失败: {StringUtils.obj_2_json_string(comment_data)}, {e}"
            )
            return None

    def _is_success(self, data):
        """
        检查JZL API响应是否成功

        :param data: 响应数据
        :return: 是否成功
        """
        if data is None:
            return False

        code = data.get("code")
        return code == 0
