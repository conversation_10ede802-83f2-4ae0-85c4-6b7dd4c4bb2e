# -*- encoding:utf-8 -*-

import datetime
import os
from shutil import copyfile


class FileUtils:
    @staticmethod
    def get_project_dir():
        return os.getcwd()

    @staticmethod
    def get_strategy_dir():
        project_path = FileUtils.get_project_dir()
        worker_path = os.path.join(project_path, "worker")
        return os.path.join(worker_path, "strategy")

    @staticmethod
    def get_log_dir():
        project_path = FileUtils.get_project_dir()
        return os.path.join(project_path, "logs")

    @staticmethod
    def get_strategy_log_dir(dt: datetime.datetime):
        project_path = FileUtils.get_project_dir()
        log_path = os.path.join(project_path, "logs")
        return os.path.join(log_path, dt.strftime('%Y%m%d'))

    @staticmethod
    def get_result_dir():
        # 1 工程路径
        project_path = FileUtils.get_project_dir()
        result_dir = os.path.join(os.path.dirname(project_path), "results")
        FileUtils.make_dir(result_dir)

        return result_dir

    @staticmethod
    def save_file(file_path, content):
        file_dir = os.path.dirname(file_path)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)

        file = open(file_path, "w", encoding='utf-8')
        if not file:
            raise None

        file.write(content)

    @staticmethod
    def copy_file(src, dst):
        FileUtils.make_file_dir(dst)
        copyfile(src, dst)

    @staticmethod
    def make_dir(dir_path):
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

    @staticmethod
    def make_file_dir(file_path):
        dir_path = os.path.dirname(file_path)
        FileUtils.make_dir(dir_path)

    @staticmethod
    def get_task_result_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "running.log")

    @staticmethod
    def get_task_mini_result_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "mini_running.log")

    @staticmethod
    def get_task_result_order_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "orders.log")

    @staticmethod
    def get_task_result_trade_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "trades.log")

    @staticmethod
    def get_task_result_statistics_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "statistics.log")

    @staticmethod
    def get_task_result_account_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "account.log")

    @staticmethod
    def get_task_result_position_file_path(task_uuid: str):
        dir_path = os.path.join(FileUtils.get_result_dir(), task_uuid)
        FileUtils.make_dir(dir_path)
        return os.path.join(dir_path, "position.log")
