# -*- coding: utf-8 -*-

import datetime

from jss_api_extend.utils import TimeUtils


class WorkDetailModel(object):

    def __init__(self,
                 platform,
                 author_id,
                 author_identity,
                 author_avatar,
                 author_name,
                 author_url,
                 work_id,
                 work_uuid,
                 url,
                 download_url,
                 long_url,
                 digest,
                 title,
                 thumbnail_link,
                 content,
                 img_urls,
                 video_urls,
                 music_url,
                 music_author_name,
                 music_id,
                 music_name,
                 publish_time,
                 publish_day,
                 location_ip,
                 read_count,
                 like_count,
                 comment_count,
                 share_count,
                 collect_count,
                 is_detail,
                 record_time):
        self.platform = platform
        self.author_id = author_id
        self.author_identity = author_identity
        self.author_avatar = author_avatar
        self.author_name = author_name
        self.author_url = author_url
        self.work_id = work_id
        self.work_uuid = work_uuid
        self.url = url
        self.download_url = download_url
        self.long_url = long_url
        self.digest = digest
        self.title = title
        self.thumbnail_link = thumbnail_link
        self.content = content
        self.img_urls = img_urls
        self.video_urls = video_urls
        self.music_url = music_url
        self.music_author_name = music_author_name
        self.music_id = music_id
        self.music_name = music_name
        self.publish_time = publish_time
        self.publish_day = publish_day
        self.location_ip = location_ip
        self.read_count = read_count
        self.like_count = like_count
        self.comment_count = comment_count
        self.share_count = share_count
        self.collect_count = collect_count
        self.record_time = record_time
        self.is_detail = is_detail

class UseWorkModel(WorkDetailModel):
    def __init__(self,
                 platform,
                 author_id,
                 author_identity,
                 author_avatar,
                 author_name,
                 author_url,
                 work_id,
                 work_uuid,
                 url,
                 download_url,
                 long_url,
                 digest,
                 title,
                 thumbnail_link,
                 content,
                 img_urls,
                 video_urls,
                 music_url,
                 music_author_name,
                 music_id,
                 music_name,
                 publish_time,
                 publish_day,
                 location_ip,
                 read_count,
                 like_count,
                 comment_count,
                 share_count,
                 collect_count,
                 is_detail,
                 record_time,
                 is_top=0):
        # 调用父类构造函数
        super().__init__(
            platform=platform,
            author_id=author_id,
            author_identity=author_identity,
            author_avatar=author_avatar,
            author_name=author_name,
            author_url=author_url,
            work_id=work_id,
            work_uuid=work_uuid,
            url=url,
            download_url=download_url,
            long_url=long_url,
            digest=digest,
            title=title,
            thumbnail_link=thumbnail_link,
            content=content,
            img_urls=img_urls,
            video_urls=video_urls,
            music_url=music_url,
            music_author_name=music_author_name,
            music_id=music_id,
            music_name=music_name,
            publish_time=publish_time,
            publish_day=publish_day,
            location_ip=location_ip,
            read_count=read_count,
            like_count=like_count,
            comment_count=comment_count,
            share_count=share_count,
            collect_count=collect_count,
            is_detail=is_detail,
            record_time = TimeUtils.get_current_ts()
        )
        # 添加新字段
        self.is_top = is_top

