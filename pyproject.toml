[project]
name = "work-monitor-project"
version = "0.1.0"
description = "Work monitoring system with chain-of-responsibility architecture"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "asyncio",
    "dataclasses",
    "typing-extensions",
    "pydantic>=2.0.0",
    "python-dotenv",
    "aiohttp",
    "requests",
    "sqlalchemy>=2.0.0",
    "alembic",
    "psycopg2-binary",
    "redis",
    "celery",
    "pyyaml",
    "jinja2",
    "click",
    "rich",
    "structlog",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio",
    "pytest-cov",
    "black",
    "isort",
    "flake8",
    "mypy",
    "pre-commit",
]

[project.scripts]
work-monitor = "work_monitor_project.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=work_monitor_project",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
]

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"


[[tool.uv.index]]
name = "private"
url = "https://packages.aliyun.com/664eec63ea478dc40fd79de8/pypi/jss-base"
username = "664f34f0fd83572df4ce4df0"
password = "z(NYAEzzr0G9"
priority = "supplemental"