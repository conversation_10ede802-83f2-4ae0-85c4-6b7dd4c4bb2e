"""
客户扩展模板 - 展示如何创建自定义处理器。

复制这个模板并修改为你的客户名称，然后实现客户特定的逻辑。
"""

import time
from typing import Dict, Any, List

# 导入核心处理器
from core.handlers.work_updater_handler import WorkUpdaterHandler
from core.handlers.dingtalk_notification import DingTalkNotification
from core.handlers.webhook_data_sync import WebhookDataSync
from core.pipeline_context import PipelineContext
from models.work_models import WorkDetailTaskModel, WorkDetailResult


class CustomWorkUpdater(WorkUpdaterHandler):
    """
    自定义作品更新处理器模板。

    继承自核心WorkUpdaterHandler，添加客户特定的处理逻辑。
    """

    def __init__(self, client_config, logger):
        super().__init__(client_config, logger)
        self.logger.info("Initialized CustomWorkUpdater")

        # 在这里添加客户特定的初始化逻辑
        self.client_name = client_config.name
        self.custom_settings = self._load_custom_settings()

    def _load_custom_settings(self) -> Dict[str, Any]:
        """加载客户特定的设置。"""
        # 在这里实现客户特定的设置加载逻辑
        return {
            "max_retry_count": 3,
            "custom_timeout": 30,
            "special_handling_platforms": ["xhs", "dy"],
        }

    def _process_single_task(
        self, context: PipelineContext, task: WorkDetailTaskModel
    ) -> WorkDetailResult:
        """
        重写单个任务处理，添加客户特定逻辑。

        在这个方法中，你可以：
        1. 添加预处理逻辑
        2. 调用父类方法
        3. 添加后处理逻辑
        """
        start_time = time.time()

        try:
            self.logger.info(f"CustomWorkUpdater processing: {task.work_url}")

            # 步骤1：客户特定的预处理
            if not self._validate_custom_task(task):
                return WorkDetailResult(
                    task=task,
                    success=False,
                    error_message="Task validation failed for custom requirements",
                    processing_time=time.time() - start_time,
                )

            # 步骤2：调用父类的核心处理逻辑
            result = super()._process_single_task(context, task)

            # 步骤3：客户特定的后处理
            if result.success:
                self._apply_custom_enhancements(result.task)

            return result

        except Exception as e:
            self.logger.error(f"CustomWorkUpdater failed for {task.work_url}: {e}")
            return WorkDetailResult(
                task=task,
                success=False,
                error_message=f"Custom processing error: {str(e)}",
                processing_time=time.time() - start_time,
            )

    def _validate_custom_task(self, task: WorkDetailTaskModel) -> bool:
        """
        客户特定的任务验证逻辑。

        在这里添加你的验证规则，例如：
        - 检查平台支持
        - 验证数据格式
        - 检查业务规则
        """
        # 示例：检查平台支持
        supported_platforms = self.custom_settings.get("special_handling_platforms", [])
        if task.platform not in supported_platforms:
            self.logger.warning(f"Platform {task.platform} not in supported list")
            return False

        # 示例：检查更新次数
        max_updates = self.custom_settings.get("max_updates", 20)
        if task.update_count > max_updates:
            self.logger.warning(f"Task {task.work_url} exceeded max updates: {task.update_count}")
            return False

        # 在这里添加更多验证逻辑

        return True

    def _apply_custom_enhancements(self, task: WorkDetailTaskModel):
        """
        应用客户特定的数据增强。

        在这里添加你的数据增强逻辑，例如：
        - 计算自定义指标
        - 添加元数据
        - 数据格式转换
        """
        if not task.author_work:
            return

        # 初始化元数据
        if not task.metadata:
            task.metadata = {}

        # 示例：计算自定义指标
        work = task.author_work
        custom_score = self._calculate_custom_score(work)
        content_type = self._classify_custom_content(work)

        # 添加客户特定的元数据
        task.metadata.update(
            {
                "custom_score": custom_score,
                "custom_content_type": content_type,
                "custom_processing_time": time.time(),
                "custom_client": self.client_name,
                "custom_version": "1.0",
            }
        )

        self.logger.info(f"Applied custom enhancements to {task.work_id}")

    def _calculate_custom_score(self, work) -> float:
        """
        计算客户特定的评分。

        在这里实现你的评分算法。
        """
        like_count = getattr(work, "like_count", 0)
        comment_count = getattr(work, "comment_count", 0)
        share_count = getattr(work, "share_count", 0)

        # 示例评分算法（可以根据客户需求修改）
        score = (like_count * 0.3) + (comment_count * 1.5) + (share_count * 2.0)
        return min(score / 50, 10.0)  # 标准化到0-10分

    def _classify_custom_content(self, work) -> str:
        """
        分类客户关心的内容类型。

        在这里实现你的内容分类逻辑。
        """
        title = getattr(work, "title", "").lower()
        content = getattr(work, "content", "").lower()

        # 示例分类逻辑（可以根据客户需求修改）
        if any(keyword in title + content for keyword in ["产品", "推荐", "测评"]):
            return "product_review"
        elif any(keyword in title + content for keyword in ["教程", "攻略", "技巧"]):
            return "tutorial"
        elif any(keyword in title + content for keyword in ["生活", "日常", "vlog"]):
            return "lifestyle"
        else:
            return "other"


class CustomNotification(DingTalkNotification):
    """
    自定义通知处理器模板。

    继承自DingTalkNotification，添加客户特定的通知逻辑。
    """

    def __init__(self, client_config, logger):
        super().__init__(client_config, logger)
        self.logger.info("Initialized CustomNotification")

    def _build_notification_message(
        self,
        work: WorkDetailTaskModel,
        notification_config: Dict[str, Any],
        threshold_result: Dict[str, Any],
    ) -> str:
        """
        重写消息构建，使用客户特定的消息格式。

        在这里自定义你的通知消息格式。
        """
        if not work.author_work:
            return f"🚨 {self.client_config.name}作品监控：{work.work_url}"

        author_work = work.author_work

        # 客户特定的消息模板
        message_parts = [
            f"🎯 **{self.client_config.name}作品阈值通知**",
            "",
            f"📱 **作品信息**",
            f"标题：{getattr(author_work, 'title', '未知')}",
            f"作者：{getattr(author_work, 'author_name', '未知')}",
            f"平台：{self._get_platform_display_name(work.platform)}",
            f"链接：{work.work_url}",
            "",
        ]

        # 添加阈值触发信息
        if threshold_result.get("threshold_checks"):
            message_parts.append("📊 **触发指标**")
            for metric, check_result in threshold_result["threshold_checks"].items():
                if check_result.get("meets_threshold"):
                    current = check_result.get("current_value", 0)
                    threshold = check_result.get("threshold_value", 0)
                    metric_name = self._get_metric_display_name(metric)
                    message_parts.append(f"🔥 {metric_name}：{current} (阈值: {threshold})")
            message_parts.append("")

        # 添加客户特定的数据
        if work.metadata:
            custom_score = work.metadata.get("custom_score")
            content_type = work.metadata.get("custom_content_type")

            if custom_score is not None or content_type:
                message_parts.append(f"🎯 **{self.client_config.name}分析**")
                if custom_score is not None:
                    message_parts.append(f"自定义评分：{custom_score:.1f}/10")
                if content_type:
                    type_names = {
                        "product_review": "产品测评",
                        "tutorial": "教程攻略",
                        "lifestyle": "生活日常",
                        "other": "其他",
                    }
                    message_parts.append(f"内容类型：{type_names.get(content_type, content_type)}")
                message_parts.append("")

        # 添加当前数据
        message_parts.extend(
            [
                "📈 **当前数据**",
                f"👍 点赞：{getattr(author_work, 'like_count', 0)}",
                f"💬 评论：{getattr(author_work, 'comment_count', 0)}",
                f"🔄 分享：{getattr(author_work, 'share_count', 0)}",
                f"⭐ 收藏：{getattr(author_work, 'collect_count', 0)}",
            ]
        )

        return "\n".join(message_parts)


class CustomDataSync(WebhookDataSync):
    """
    自定义数据同步处理器模板。

    继承自WebhookDataSync，添加客户特定的同步逻辑。
    """

    def __init__(self, client_config, logger):
        super().__init__(client_config, logger)
        self.logger.info("Initialized CustomDataSync")

    def _apply_field_mapping(
        self, work: WorkDetailTaskModel, field_mapping: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        重写字段映射，添加客户特定的字段。

        在这里添加你的自定义字段映射逻辑。
        """
        # 获取基础映射
        mapped_fields = super()._apply_field_mapping(work, field_mapping)

        # 添加客户特定的字段
        if work.metadata:
            custom_score = work.metadata.get("custom_score")
            content_type = work.metadata.get("custom_content_type")

            if custom_score is not None:
                mapped_fields["自定义评分"] = f"{custom_score:.1f}"

            if content_type:
                type_names = {
                    "product_review": "产品测评",
                    "tutorial": "教程攻略",
                    "lifestyle": "生活日常",
                    "other": "其他",
                }
                mapped_fields["内容类型"] = type_names.get(content_type, content_type)

            # 添加处理时间
            processing_time = work.metadata.get("custom_processing_time")
            if processing_time:
                from datetime import datetime

                dt = datetime.fromtimestamp(processing_time)
                mapped_fields["处理时间"] = dt.strftime("%Y-%m-%d %H:%M:%S")

        return mapped_fields
