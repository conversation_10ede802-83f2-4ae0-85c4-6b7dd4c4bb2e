# -*- coding: utf-8 -*-

from dao.db_adapter import g_database_product


class WorkDetailTaskNotifyRecordRepository(object):
    """
    对应表格 bardata 操作
    """
    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, user_id, record_id, submit_user, work_id, platform):
        sql = '''
            INSERT INTO `work_detail_task_notify_record`(
            `user_id`, `record_id`, `sumbit_user`, `work_id`, `platform`) 
            VALUES (%s,%s,%s,%s,%s);
            '''

        args = (user_id, record_id, submit_user, work_id, platform)
        return self.gdb_datasource.insert(sql=sql, args=args)

    def query(self, user_id, work_id):
        sql = 'select * from work_detail_task_notify_record where user_id=%s and work_id=%s'
        return self.gdb_datasource.fetch_all(sql, (user_id, work_id))

    def has_record(self, user_id, work_id):
        rows = self.query(user_id, work_id)
        return len(rows) > 0
