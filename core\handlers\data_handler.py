"""
数据获取Handler - 负责从数据源获取待处理的作品数据

支持多种数据获取方式：
1. 从服务器查询 (需要api_token) - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法
2. 从多维表获取 (使用jss-api-extend) - 参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法
3. 客户特定的数据获取器 - 支持每个客户的个性化需求

架构设计：
- DataHandler: 基础数据获取处理器
- ClientSpecificDataFetcher: 客户特定的数据获取器接口
- 具体实现类：为每个客户提供定制化的数据获取逻辑
"""

import requests
from typing import List, Dict, Any, Optional

from models.work_models import WorkDetailTaskModel
from .base_handler import BaseHandler
from config.client_config import ClientConfig
from ..exceptions import DataFetchException
from ..pipeline_context import PipelineContext
from jss_api_extend import ServiceClient, Environment, BaseDingTalkBitableNotifier
from utils.time_utils import TimeUtils
from utils.url_utils import UrlUtils


class ConfigurableDataFetcher:
    """
    可配置的数据获取器

    通过配置文件定义过滤条件和字段映射，无需为每个客户编写代码

    """

    def __init__(self, client_config: ClientConfig, logger):
        self.client_config = client_config
        self.logger = logger
        self.notifier = BaseDingTalkBitableNotifier(
            logger,
            client_config.bitable.get("app_key"),
            client_config.bitable.get("app_secret"),
            client_config.bitable.get("agent_id"),
            client_config.bitable.get("operator_id", "JQm5uuCxkjI6pjP08ZJ7aQiEiE")
        )
        self.service_client = ServiceClient(
            environment=Environment.LOCAL,
            logger_=logger,
            api_token=client_config.user_info.get("api-token")
        )

        # 获取字段映射配置
        self.field_mapping = client_config.data_fetcher.get("field_mapping", {})
        self.custom_logic = client_config.data_fetcher.get("custom_logic", {})

    def fetch_data(self, context: PipelineContext) -> List[WorkDetailTaskModel]:
        """
        获取数据 - 支持两种方式：
        1. 从服务器查询（如果有api_token）
        2. 直接从多维表查询（使用配置的过滤条件）
        """
        # 如果有api_token，先尝试从服务器获取表格信息
        api_token = self.client_config.user_info.get("api-token")
        if api_token:
            table_info = self._query_update_table_one()
            if table_info:
                dentry_uuid = table_info.get("dentryUuid")
                id_or_name = table_info.get("idOrName")
                max_update_times = table_info.get("updateTimes")

                if all([dentry_uuid, id_or_name]):
                    self.logger.info(f"从服务器获取表格信息: {dentry_uuid}/{id_or_name}")
                    return self._fetch_from_bitable(dentry_uuid, id_or_name, max_update_times)

        # 否则直接从配置获取表格信息
        dentry_uuid = self.client_config.bitable.get("dentryUuid")
        id_or_name = self.client_config.bitable.get("idOrName", "基础数据表")

        if not dentry_uuid:
            self.logger.error(f"客户 {self.client_config.name} 缺少 dentryUuid 配置")
            return []

        self.logger.info(f"从配置获取表格信息: {dentry_uuid}/{id_or_name}")
        return self._fetch_from_bitable(dentry_uuid, id_or_name)

    def _fetch_from_bitable(self, dentry_uuid: str, id_or_name: str, max_update_times: Optional[int] = None) -> List[WorkDetailTaskModel]:
        """从多维表获取数据"""
        # 构建过滤条件
        filter_conditions = self.get_filter_conditions(max_update_times)

        # 查询记录
        record_list = self.notifier.query_records(
            dentry_uuid,
            id_or_name,
            filter_conditions=filter_conditions
        )

        if not record_list:
            self.logger.info(f"数据表 {dentry_uuid} 无符合条件的数据")
            return []

        return self._convert_records_to_tasks(record_list, dentry_uuid, id_or_name)

    def get_filter_conditions(self, max_update_times: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取过滤条件 - 优先使用配置中的条件，否则使用默认逻辑
        """
        # 1. 如果配置中有明确的过滤条件，直接使用
        configured_conditions = self.client_config.data_fetcher.get("filter_conditions", [])
        if configured_conditions:
            self.logger.info(f"使用配置的过滤条件: {len(configured_conditions)} 个条件")
            return configured_conditions

        # 2. 否则根据配置和参数动态构建过滤条件
        conditions = []
        filter_rules = self.client_config.data_fetcher.get("filter_rules", {})

        # 更新次数限制
        update_limit = max_update_times or filter_rules.get("update_times_limit")
        if update_limit:
            conditions.append({
                "field": self._get_field_name("update_count_field", "更新次数"),
                "operator": "less",
                "value": [update_limit]
            })

        # 平台过滤
        platform = filter_rules.get("platform")
        if platform and platform != "all":
            platform_values = platform if isinstance(platform, list) else [platform]
            conditions.append({
                "field": self._get_field_name("platform_field", "平台"),
                "operator": "equal",
                "value": platform_values
            })

        # 状态过滤
        status = filter_rules.get("status")
        if status:
            status_values = status if isinstance(status, list) else [status]
            conditions.append({
                "field": self._get_field_name("status_field", "状态"),
                "operator": "equal",
                "value": status_values
            })

        # 作品链接不为空
        conditions.append({
            "field": self._get_field_name("work_url_field", "作品链接"),
            "operator": "notEmpty",
            "value": [""]
        })

        self.logger.info(f"动态构建过滤条件: {len(conditions)} 个条件")
        return conditions

    def _get_field_name(self, mapping_key: str, default_name: str) -> str:
        """获取字段名称，支持字段映射"""
        return self.field_mapping.get(mapping_key, default_name)

    def _query_update_table_one(self) -> Optional[Dict[str, Any]]:
        """查询单表任务列表"""
        try:
            response = self.service_client.query_table_to_update()
            if not response:
                self.logger.warning("查询任务列表失败：响应为空")
                return None

            if not self.service_client.is_response_success(response):
                self.logger.error(f"查询任务列表失败：{response.get('msg', '未知错误')}")
                return None

            table_info = response.get("result", {})
            if not table_info:
                self.logger.info("没有待更新的任务")
                return None

            self.logger.info(f"成功获取任务: {table_info}")
            return table_info

        except Exception as e:
            self.logger.error(f"查询任务列表异常: {e}")
            return None

    def _convert_records_to_tasks(self, record_list: List[Dict], dentry_uuid: str, id_or_name: str) -> List[WorkDetailTaskModel]:
        """将记录转换为任务模型"""
        task_list = []
        covert_fail_queue = []

        # 获取字段映射
        work_url_field = self._get_field_name("work_url_field", "作品链接")
        user_field = self._get_field_name("user_field", "运营人员")
        update_count_field = self._get_field_name("update_count_field", "更新次数")
        extends_field = self._get_field_name("extends_field", "")

        for record in record_list:
            fields = record.get("fields", {})
            work_url = fields.get(work_url_field)

            if not work_url:
                continue

            # 如果启用了7天规则，进行检查
            if self.custom_logic.get("enable_7_days_rule", False):
                update_count = fields.get(update_count_field, 0)
                max_update_times = self.custom_logic.get("max_update_times", 5)

                if update_count >= max_update_times:
                    if not self._check_7_days_rule(record.get("created_time"), record.get("last_modified_time")):
                        continue

            result = self._extract_task_item(work_url)
            if result is None:
                covert_fail_queue.append(work_url)
                continue

            platform, work_id = result
            if not platform or not work_id:
                self.logger.warning(
                    f"提取到的 platform 或 work_id 为空，跳过。Platform: {platform}, WorkId: {work_id}"
                )
                continue

            work_task = WorkDetailTaskModel(
                id=None,
                work_url=work_url,
                platform=platform,
                work_id=work_id,
                row_id=record.get("id"),
                submit_user=fields.get(user_field, ""),
                dentry_uuid=dentry_uuid,
                id_or_name=id_or_name,
                update_count=fields.get(update_count_field, 0),
                extends=fields.get(extends_field, "") if extends_field else ""
            )

            task_list.append(work_task)

        if covert_fail_queue:
            self.logger.warning(f"转换失败的URL数量: {len(covert_fail_queue)}")

        self.logger.info(f"成功转换 {len(task_list)} 个任务")
        return task_list

    def _check_7_days_rule(self, created_time: int, last_modified_time: int) -> bool:
        """检查7天规则"""
        if not created_time or not last_modified_time:
            return True

        # 转换为秒（如果是毫秒时间戳）
        if created_time > 10**10:
            created_time = created_time // 1000
        if last_modified_time > 10**10:
            last_modified_time = last_modified_time // 1000

        # 检查是否超过7天
        current_time = TimeUtils.get_current_timestamp()
        days_since_created = (current_time - created_time) / (24 * 3600)
        days_since_modified = (current_time - last_modified_time) / (24 * 3600)

        return days_since_created >= 7 or days_since_modified >= 7

    def _extract_task_item(self, work_url: str):
        """提取链接内信息：链接转化， work_id 提取， platform 信息提取"""
        if not work_url:
            self.logger.error("work_url为空")
            return None

        origin_url = work_url
        normal_url = origin_url

        self.logger.info(f"原始 work_url: {work_url}")

        # 分享链接确认
        if UrlUtils.check_short_url(normal_url):
            normal_url = self._url_redirection(normal_url)

        if not normal_url:
            self.logger.error(f"链接转化失败，短链转长连接失败 :{work_url}")
            return None

        # 去空格
        normal_url = normal_url.strip()

        # 提取平台信息
        platform = UrlUtils.select_platform(normal_url)

        if not platform:
            self.logger.error(f"format fail message :{origin_url}")
            return None

        if platform == "xhs":
            work_id = UrlUtils.get_xhs_work_id(normal_url)
        else:
            work_id = UrlUtils.get_dy_work_id(normal_url)

        return platform, work_id

    def _url_redirection(self, work_url: str) -> Optional[str]:
        """对短链的处理"""
        try:
            response = requests.get(work_url, allow_redirects=True, timeout=30)
            return response.url
        except Exception as e:
            self.logger.error(f"{e}")
            return None





class DataHandler(BaseHandler):
    """
    数据获取处理器

    职责：
    - 从配置的数据源获取待处理的作品列表
    - 统一处理为标准的数据格式
    - 应用过滤规则
    - 设置上下文中的待处理数据

    支持通过配置文件定制每个客户的数据获取逻辑：
    - 自定义过滤条件
    - 字段映射
    - 特殊业务逻辑（如7天规则）
    """

    def __init__(self, client_config: ClientConfig, logger_, data_fetcher: Optional[ConfigurableDataFetcher] = None):
        super().__init__(client_config, logger_)

        # 如果没有提供特定的数据获取器，使用可配置的数据获取器
        if data_fetcher is None:
            self.data_fetcher = ConfigurableDataFetcher(client_config, logger_)
        else:
            self.data_fetcher = data_fetcher

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理数据获取逻辑
        """
        try:
            self.logger.info(f"Starting data fetch for client: {context.client_name}")

            # 使用客户特定的数据获取器获取数据
            task_list = self.data_fetcher.fetch_data(context)

            # 设置到上下文
            context.task_list = task_list if task_list else []

            self.logger.info(f"Fetched {len(context.task_list)} tasks for processing")

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Data fetch failed: {e}")
            raise DataFetchException(f"Failed to fetch data: {str(e)}")

    def get_filter_conditions(self) -> List[Dict[str, Any]]:
        """
        获取当前数据获取器的过滤条件
        """
        return self.data_fetcher.get_filter_conditions()