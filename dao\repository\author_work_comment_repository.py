# -*- coding: utf-8 -*-

from typing import List

from dao.db_adapter import g_database_product
from dao.model.author_work_comment import AuthorWorkComment
from dao.model.work_comment import WorkComment


class AuthorWorkCommentRepository(object):
    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, work_id, platform, work_comment: AuthorWorkComment):
        sql = '''
            INSERT INTO author_work_comments (
                work_id, work_url, platform, comment_uuid, commenter_name,
                commenter_id, commenter_url, content, publish_time, like_count,
                location_ip, first_cid, has_sub, is_sub, sec_uid, news_parent_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, 
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            );'''
        args = (work_id, work_comment.work_url, platform,
                work_comment.comment_uuid, work_comment.commenter_name,
                work_comment.commenter_id, work_comment.commenter_url,
                work_comment.content, work_comment.publish_time, work_comment.like_count,
                work_comment.location_ip, work_comment.first_cid, work_comment.has_sub,
                work_comment.is_sub, work_comment.sec_uid, work_comment.news_parent_id)

        return self.gdb_datasource.insert(sql=sql, args=args)

    def query_comment_info(self, comment_uuid):
        sql = 'select * from author_work_comments where comment_uuid=%s'
        return self.gdb_datasource.fetch_all(sql, (comment_uuid))

    def query_comment_list(self, work_id):
        sql = 'select * from author_work_comments where work_id=%s'
        return self.gdb_datasource.fetch_all(sql, (work_id))

    def batch_insert_comments(self, work_id, platform,
                              work_comments: List[AuthorWorkComment]) -> int:
        args_list = []

        sql = '''
            INSERT INTO author_work_comments (
                work_id, work_url, platform, comment_uuid, commenter_name,
                commenter_id, commenter_url, content, publish_time, like_count,
                location_ip, first_cid, has_sub, is_sub, sec_uid, news_parent_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, 
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            );'''

        for work_comment in work_comments:
            args = (work_id, work_comment.work_url, platform,
                    work_comment.comment_uuid, work_comment.commenter_name,
                    work_comment.commenter_id, work_comment.commenter_url,
                    work_comment.content, work_comment.publish_time, work_comment.like_count,
                    work_comment.location_ip, work_comment.first_cid, work_comment.has_sub,
                    work_comment.is_sub, work_comment.sec_uid, work_comment.news_parent_id)

            args_list.append(args)

        return self.gdb_datasource.batch_insert(sql, args_list)

    def batch_insert_comments_ignore_duplicates(self, work_id, platform,
                                                work_comments: List[AuthorWorkComment]) -> int:
        """
        批量插入评论，忽略重复记录（基于comment_uuid唯一键）
        """
        args_list = []

        sql = '''
            INSERT IGNORE INTO author_work_comments (
                work_id, work_url, platform, comment_uuid, commenter_name,
                commenter_id, commenter_url, content, publish_time, like_count,
                location_ip, first_cid, has_sub, is_sub, sec_uid, news_parent_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            );'''

        for work_comment in work_comments:
            args = (work_id, work_comment.work_url, platform,
                    work_comment.comment_uuid, work_comment.commenter_name,
                    work_comment.commenter_id, work_comment.commenter_url,
                    work_comment.content, work_comment.publish_time, work_comment.like_count,
                    work_comment.location_ip, work_comment.first_cid, work_comment.has_sub,
                    work_comment.is_sub, work_comment.sec_uid, work_comment.news_parent_id)

            args_list.append(args)

        return self.gdb_datasource.batch_insert(sql, args_list)

    @staticmethod
    def build_author_work_comment_entity(_row, author_name, title):
        return WorkComment(
            _id=_row['id'],
            author_id=_row['author_id'],
            author_name=author_name,
            work_id=_row['work_id'],
            work_title=title,
            work_url=_row['work_url'],
            platform="小红书",
            comment_uuid=_row['comment_uuid'],
            commenter_name=_row['commenter_name'],
            commenter_id=_row['commenter_id'],
            commenter_url=_row['commenter_url'],
            content=_row['content'],
            publish_time=_row['publish_time'],
            like_count=_row['like_count'],
            location_ip=_row['location_ip'],
            text_polarity=_row['text_polarity'],
            record_time=_row['record_time'],
        )
