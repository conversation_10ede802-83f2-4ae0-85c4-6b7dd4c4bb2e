from datetime import datetime

from jss_api_extend.client.tikhub_douyin_client import TikhubDouyinClient
from jss_api_extend.adapter.models.work_detail_model import UseWorkModel
from jss_api_extend.adapter.models.author_profile_model import AuthorProfileModel
from jss_api_extend.utils import TimeUtils


class DouyinUserHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.tikhub_client = TikhubDouyinClient(logger_)

    def query_work_tracing_by_tikhub(self, sec_uid: str, start_time: int):

        return self._query_work_tracing_by_tikhub(sec_uid, start_time)

    def get_user_profile(self, sec_uid: str) -> AuthorProfileModel:
        """
        获取用户主页数据

        :param sec_uid: 用户的sec_uid
        :return: AuthorProfileModel 用户主页数据模型
        """
        try:
            response = self.tikhub_client.fetch_user_profile(sec_uid)

            if not response:
                self.logger_.error(f"获取用户主页数据失败，响应为空: sec_uid={sec_uid}")
                return None

            data = response
            user_data = data.get("data", {}).get("user")
            if not user_data:
                self.logger_.error(f"用户数据不存在: sec_uid={sec_uid}")
                return None

            author_profile = AuthorProfileModel(
                signature=user_data.get("signature", ""),
                sec_uid=user_data.get("sec_uid", ""),
                nickname=user_data.get("nickname", ""),
                work_count=user_data.get("aweme_count", 0),
                ip_location=user_data.get("ip_location", ""),
                total_favorited=user_data.get("total_favorited", 0),
                follower_count=user_data.get("follower_count", 0),
                following_count=user_data.get("following_count", 0),
                favoriting_count=user_data.get("favoriting_count", 0),
                gender=user_data.get("gender", -1),
            )

            self.logger_.info(
                f"成功获取用户主页数据: sec_uid={sec_uid}, nickname={author_profile.nickname}"
            )
            return author_profile

        except Exception as e:
            self.logger_.error(f"获取用户主页数据异常: sec_uid={sec_uid}, error={e}")
            return None

    def _query_work_tracing_by_tikhub(self, sec_uid: str, start_time: int):
        """
        账号历史作品追溯 从 start_time ~ 当前
        注意
        最前页为置顶作品，要检查置顶作品（is_top = 1）是否处于时间区间内,如果是加入返回结果列表,
        继续检查，到当前页面结束前如果查询到数据出现发布时间 < start_time， t停止翻页，将json转为 对象返回

        :param sec_uid: 用户的sec_uid
        :param start_time: 开始时间戳
        :return: UseWorkModel 列表
        """
        result_works = []
        max_cursor = 0
        has_more = True

        try:
            while has_more:
                self.logger_.info(
                    f"正在获取用户作品，sec_uid={sec_uid}, max_cursor={max_cursor}"
                )

                # 调用 TikHub API 获取用户作品
                response = self.tikhub_client.fetch_user_post_videos(
                    sec_user_id=sec_uid, max_cursor=max_cursor, count=20
                )

                if not response or response.get("code") != 200:
                    self.logger_.error(f"获取用户作品失败，response={response}")
                    break

                data = response.get("data", {})
                video_list = data.get("aweme_list", [])

                if not video_list:
                    self.logger_.info("没有更多作品数据")
                    break

                # 处理当前页的作品
                should_stop = False
                for video in video_list:
                    is_top = video.get("is_top", 0)
                    create_time = video.get("create_time", 0)

                    if is_top == 1:
                        if create_time >= start_time:
                            self.logger_.info(
                                f"找到符合条件的置顶作品，create_time={create_time}"
                            )
                            try:
                                work_model = (
                                    TikHubDouyinUserWorkDetailHelper.parse_work_detail(
                                        video
                                    )
                                )
                                result_works.append(work_model)
                            except Exception as e:
                                self.logger_.error(f"解析置顶作品失败: {e}")
                        continue

                    if create_time < start_time:
                        self.logger_.info(
                            f"发现作品发布时间早于开始时间，停止翻页。create_time={create_time}, start_time={start_time}"
                        )
                        should_stop = True
                        break

                    # 符合时间条件的普通作品
                    self.logger_.info(f"找到符合条件的作品，create_time={create_time}")
                    try:
                        work_model = TikHubDouyinUserWorkDetailHelper.parse_work_detail(
                            video
                        )
                        result_works.append(work_model)
                    except Exception as e:
                        self.logger_.error(f"解析作品失败: {e}")

                if should_stop:
                    break

                has_more = data.get("has_more", 0)
                if has_more == 1:
                    max_cursor = data.get("max_cursor", 0)
                    if max_cursor == 0:
                        self.logger_.info("max_cursor为0，停止翻页")
                        break
                else:
                    self.logger_.info("没有更多页面")
                    break

        except Exception as e:
            self.logger_.error(f"查询用户作品追溯异常: {e}")

        self.logger_.info(f"用户作品追溯完成，共获取到 {len(result_works)} 个作品")
        return result_works


class TikHubDouyinUserWorkDetailHelper(object):

    @staticmethod
    def parse_work_detail(json_data):
        """
        解析JSON数据并返回一个WorkDetailModel实例。

        Args:
            json_data (dict): 从JSON文件加载的字典数据。

        Returns:
            WorkDetailModel: 包含解析后数据的模型实例。
        """

        # 安全地获取嵌套字典中的值
        def safe_get(data, keys, default=None):
            for key in keys:
                if isinstance(data, dict):
                    data = data.get(key)
                else:
                    return default
            return data if data is not None else default

        try:
            # 提取作者信息
            author_info = json_data.get("author", {})
            author_id = author_info.get("sec_uid")
            author_identity = author_info.get("sec_uid")
            author_avatar = safe_get(author_info, ["avatar_thumb", "url_list", 0])
            author_name = author_info.get("nickname")
            author_url = (
                f"https://www.douyin.com/user/{author_info.get('sec_uid')}"
                if author_info.get("sec_uid")
                else None
            )

            # 提取作品信息
            work_id = json_data.get("aweme_id")
            url = json_data.get("share_url")
            title = json_data.get("desc")
            content = json_data.get("desc")

            # 提取统计数据
            statistics = json_data.get("statistics", {})
            like_count = statistics.get("digg_count")
            comment_count = statistics.get("comment_count")
            share_count = statistics.get("share_count")
            collect_count = statistics.get("collect_count")
            read_count = statistics.get("play_count")  # play_count 经常用作阅读/播放数

            # 提取媒体链接
            video_info = json_data.get("video", {})
            thumbnail_link = safe_get(video_info, ["origin_cover", "url_list", 0])
            video_urls = safe_get(video_info, ["play_addr", "url_list"], [])
            # 检查下载是否被允许
            download_url = None
            if safe_get(json_data, ["video_control", "allow_download"], False):
                download_url = safe_get(video_info, ["download_addr", "url_list", 0])

            # 提取音乐信息
            music_info = json_data.get("music", {})
            music_url = safe_get(music_info, ["play_url", "url_list", 0])
            music_author_name = music_info.get("author")
            music_id = music_info.get("id")
            music_name = music_info.get("title")

            # 处理时间戳
            publish_timestamp = json_data.get("create_time")
            publish_time = None
            publish_day = None
            if publish_timestamp:
                dt_object = datetime.fromtimestamp(publish_timestamp)
                publish_time = dt_object.strftime("%Y-%m-%d %H:%M:%S")
                publish_day = dt_object.strftime("%Y-%m-%d")

            is_top = json_data.get("is_top", 0)

            # 实例化模型
            work_detail = UseWorkModel(
                platform="douyin",  # 根据URL推断平台为抖音
                author_id=author_id,
                author_identity=author_identity,
                author_avatar=author_avatar,
                author_name=author_name,
                author_url=author_url,
                work_id=work_id,
                work_uuid=work_id,  # 使用 work_id 作为 uuid
                url=url,
                download_url=download_url,
                long_url=url,  # 使用 share_url 作为 long_url
                digest=title,
                title=title,
                thumbnail_link=thumbnail_link,
                content=content,
                img_urls=json_data.get("images") or [],  # 视频作品通常没有图片
                video_urls=video_urls,
                music_url=music_url,
                music_author_name=music_author_name,
                music_id=music_id,
                music_name=music_name,
                publish_time=publish_time,
                publish_day=publish_day,
                location_ip=json_data.get("ip_attribution"),
                read_count=read_count,
                like_count=like_count,
                comment_count=comment_count,
                share_count=share_count,
                collect_count=collect_count,
                is_detail=1,
                is_top=is_top,
                record_time=TimeUtils.get_current_ts(),
            )

            return work_detail
        except Exception as e:
            raise e
