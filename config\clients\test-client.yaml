bitable:
  dentryUuid: your-dentry-uuid
  idOrName: your-table-name
  webhook: your-webhook-url
data_fetcher:
  filter_rules:
    update_times_limit: 5
  source_type: dingtalk_bitable
data_sync:
  batch_size: 100
  field_mapping:
    作品标题: title
    分享数: share_count
    收藏数: collect_count
    点赞数: like_count
    评论数: comment_count
name: test-client
notification:
  channels:
  - dingtalk
  message_template: 作品阈值通知：标题={title}, 作者={author_name}
  threshold:
    like_count: 50
schedule:
  cron: 0 */2 * * *
  max_tasks: 100
user_info:
  api-token: your-api-token-here
  tenant: your-tenant
  user_id: 0
work_updater:
  batch_size: 50
  platforms:
  - xhs
  - dy
