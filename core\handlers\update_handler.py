"""
更新逻辑Handler - 负责调用平台API获取最新的作品详情数据
"""

import logging
import time
from typing import List
from abc import ABC, abstractmethod

from .base_handler import BaseHandler
from config.client_config import ClientConfig
from ..pipeline_context import PipelineContext
from ..exceptions import WorkUpdateException
from models.work_models import WorkDetailTaskModel


class UpdateHandler(BaseHandler):
    """
    更新逻辑处理器基类

    职责：
    - 调用各平台API获取作品最新数据
    - 更新作品详情信息
    - 处理批量更新逻辑
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.logger = logger.getChild(self.__class__.__name__)
        self.platform_handlers = {}

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理更新逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"Starting update process for {len(context.task_list)} tasks")

            # 初始化平台处理器
            self._initialize_platform_handlers()

            # 批量处理任务
            updated_tasks = self._process_tasks_batch(context.task_list, context)

            # 设置到上下文
            context.task_list = updated_tasks
            context.successful_updates = len(
                [t for t in updated_tasks if t.author_work is not None]
            )
            context.failed_updates = len(updated_tasks) - context.successful_updates

            self.logger.info(
                f"Update completed: {context.successful_updates} successful, {context.failed_updates} failed"
            )

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Update process failed: {e}")
            raise WorkUpdateException(f"Failed to update works: {str(e)}")

    def _initialize_platform_handlers(self):
        """初始化平台处理器"""
        try:
            # 动态导入平台处理器
            self._import_platform_handlers()
        except Exception as e:
            self.logger.warning(f"Failed to import some platform handlers: {e}")

    def _import_platform_handlers(self):
        """导入平台处理器"""
        try:
            # 小红书
            from platform_handlers.xhs_handler import XhsHandler

            self.platform_handlers["xhs"] = XhsHandler(self.logger)
        except ImportError:
            self.logger.warning("XHS handler not available")

        try:
            # 抖音
            from platform_handlers.dy_handler import DyHandler

            self.platform_handlers["dy"] = DyHandler(self.logger)
        except ImportError:
            self.logger.warning("DY handler not available")

        try:
            # 快手
            from platform_handlers.ks_handler import KsHandler

            self.platform_handlers["ks"] = KsHandler(self.logger)
        except ImportError:
            self.logger.warning("KS handler not available")

        try:
            # 微信视频号
            from platform_handlers.wxvideo_handler import WxvideoHandler

            self.platform_handlers["wxvideo"] = WxvideoHandler(self.logger)
        except ImportError:
            self.logger.warning("WXVideo handler not available")

    def _process_tasks_batch(
        self, tasks: List[WorkDetailTaskModel], context: PipelineContext
    ) -> List[WorkDetailTaskModel]:
        """
        批量处理任务

        Args:
            tasks: 待处理的任务列表
            context: 工作上下文

        Returns:
            处理后的任务列表
        """
        updated_tasks = []
        batch_size = self._get_batch_size()

        for i in range(0, len(tasks), batch_size):
            batch = tasks[i : i + batch_size]
            self.logger.info(f"Processing batch {i//batch_size + 1}: {len(batch)} tasks")

            for task in batch:
                try:
                    result = self._process_single_task(task, context)
                    updated_tasks.append(result)

                    # 添加延迟避免API限制
                    time.sleep(0.1)

                except Exception as e:
                    self.logger.error(f"Failed to process task {task.work_url}: {e}")
                    # 添加失败的任务
                    task.error_message = str(e)
                    updated_tasks.append(task)

        return updated_tasks

    def _process_single_task(
        self, task: WorkDetailTaskModel, context: PipelineContext
    ) -> WorkDetailTaskModel:
        """
        处理单个任务

        Args:
            task: 待处理的任务
            context: 工作上下文

        Returns:
            处理后的任务
        """
        try:
            self.logger.debug(f"Processing task: {task.work_url}")

            # 获取平台处理器
            platform_handler = self.platform_handlers.get(task.platform)
            if not platform_handler:
                raise WorkUpdateException(f"No handler available for platform: {task.platform}")

            # 调用平台API获取作品详情
            work_detail = platform_handler.get_work_detail(task.work_url)

            if work_detail:
                # 更新任务信息
                task.author_work = work_detail
                task.update_count += 1
                task.last_update_time = time.time()

                # 检查是否触发阈值
                task.threshold = self._check_threshold(work_detail)

                self.logger.debug(f"Successfully updated task: {task.work_id}")
            else:
                self.logger.warning(f"No work detail returned for: {task.work_url}")

            return task

        except Exception as e:
            self.logger.error(f"Failed to process single task {task.work_url}: {e}")
            task.error_message = str(e)
            return task

    def _check_threshold(self, work_detail) -> str:
        """
        检查是否触发阈值

        Args:
            work_detail: 作品详情

        Returns:
            "是" 或 "否"
        """
        try:
            # 获取阈值配置
            threshold_config = self._get_threshold_config()

            like_count = getattr(work_detail, "like_count", 0)
            comment_count = getattr(work_detail, "comment_count", 0)
            share_count = getattr(work_detail, "share_count", 0)

            # 检查是否达到任一阈值
            if (
                like_count >= threshold_config.get("like_count", float("inf"))
                or comment_count >= threshold_config.get("comment_count", float("inf"))
                or share_count >= threshold_config.get("share_count", float("inf"))
            ):
                return "是"

            return "否"

        except Exception as e:
            self.logger.warning(f"Failed to check threshold: {e}")
            return "否"

    def _get_batch_size(self) -> int:
        """获取批处理大小"""
        work_updater_config = self.client_config.work_updater_config or {}
        return work_updater_config.get("batch_size", 10)

    def _get_threshold_config(self) -> dict:
        """获取阈值配置"""
        notification_config = self.client_config.notification_config or {}
        threshold_rules = notification_config.get("threshold_rules", {})

        # 优先使用客户特定的阈值
        client_threshold = threshold_rules.get(self.client_config.name)
        if client_threshold:
            return client_threshold

        # 使用默认阈值
        return threshold_rules.get(
            "default", {"like_count": 100, "comment_count": 30, "share_count": 20}
        )


class DatabaseUpdateHandler(UpdateHandler):
    """
    带数据库集成的更新处理器
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self._repository = None

    def _process_single_task(
        self, task: WorkDetailTaskModel, context: PipelineContext
    ) -> WorkDetailTaskModel:
        """
        处理单个任务，包含数据库操作
        """
        try:
            # 先调用父类方法获取最新数据
            updated_task = super()._process_single_task(task, context)

            # 保存到数据库
            if updated_task.author_work and not updated_task.error_message:
                self._save_to_database(updated_task)

            return updated_task

        except Exception as e:
            self.logger.error(f"Database update failed for {task.work_url}: {e}")
            task.error_message = str(e)
            return task

    def _save_to_database(self, task: WorkDetailTaskModel):
        """保存到数据库"""
        try:
            repository = self._get_repository()
            if repository:
                repository.save_work_detail(task)
                self.logger.debug(f"Saved to database: {task.work_id}")
        except Exception as e:
            self.logger.warning(f"Failed to save to database: {e}")

    def _get_repository(self):
        """获取数据库仓库"""
        if self._repository is None:
            try:
                from dao.work_detail_refresh_repository import WorkDetailRefreshRepository

                self._repository = WorkDetailRefreshRepository()
            except ImportError:
                self.logger.warning("Database repository not available")

        return self._repository
