# Work Monitor Project

A flexible work monitoring system built with chain-of-responsibility architecture for managing client-specific work data updates from various platforms (小红书, 抖音, etc.).

## 🎯 Project Overview

This system provides a unified framework for:
- **Data Fetching** - Retrieving work data from various sources (DingTalk Bitable, etc.)
- **Work Updating** - Fetching detailed work information from platforms (XHS, Douyin, etc.)
- **Data Synchronization** - Syncing updated data back to target systems
- **Notification** - Sending threshold-based notifications to users
- **Scheduling** - Managing client-specific execution schedules with environment isolation

## 🏗️ Architecture

The system uses a **Chain of Responsibility** pattern where each processing step is an independent handler that can be customized per client:

1. **DataFetcherHandler** - Retrieves tasks to be updated
2. **WorkUpdaterHandler** - Fetches platform-specific work details
3. **DataSyncHandler** - Synchronizes data to target systems
4. **NotificationHandler** - Sends threshold-based notifications

## 📁 Project Structure

```
work_monitor_project/
├── config/                    # Configuration management
│   ├── base_config.py        # Base configuration classes
│   └── clients/              # Client-specific configurations
│       ├── shuiyuntu.yaml    # 水云兔 client config
│       └── taotian.yaml      # 淘天 client config
├── core/                     # Core system components
│   ├── handlers/             # Handler implementations
│   │   ├── base_handler.py   # Base handler interface
│   │   └── __init__.py
│   ├── chain_executor.py     # Chain execution management
│   ├── context.py           # Processing context classes
│   ├── exceptions.py        # System exceptions
│   └── __init__.py
├── models/                   # Data models
│   ├── work_models.py       # Work-related data models
│   └── __init__.py
├── services/                 # Service classes
│   ├── config_manager.py    # Configuration management
│   └── __init__.py
├── clients/                  # Client implementations (to be created)
├── dao/                     # Data access objects (existing)
├── utils/                   # Utility functions (existing)
├── jss_api_extend/          # Private package for platform APIs
├── old/                     # Legacy implementation for reference
├── main.py                  # Application entry point
└── test_chain_architecture.py # Architecture test
```

## ✨ Key Features

- **Client Isolation** - Each client has independent configuration, logging, and execution environment
- **Flexible Configuration** - YAML-based configuration with validation
- **Extensible Handlers** - Easy to customize or override specific processing steps
- **Error Handling** - Comprehensive error tracking and recovery
- **Monitoring** - Built-in metrics and execution summaries

## 🚀 Quick Start

### Installation

```bash
# Install dependencies using uv (recommended)
uv pip install click pyyaml

# Or using pip
pip install click pyyaml
```

### Basic Usage

```bash
# List available clients
python -m main list-clients

# Show detailed client configurations
python -m main list-clients --show-config

# Validate all configurations
python -m main validate

# Validate specific client
python -m main validate --client shuiyuntu

# Create new client configuration
python -m main create-config new-client

# Run for specific client (implementation in progress)
python -m main run --client shuiyuntu
```

### Configuration

Each client has a YAML configuration file in `config/clients/`. Example:

```yaml
name: "水云兔"
schedule:
  cron: "0 */2 * * *"  # Every 2 hours
  max_tasks: 100

data_fetcher:
  source_type: "dingtalk_bitable"
  filter_rules:
    update_times_limit: 5

work_updater:
  platforms: ["xhs", "dy"]
  batch_size: 50

notification:
  threshold:
    like_count: 50
  message_template: "作品阈值通知：标题={title}, 作者={author_name}"
```

## 🧪 Testing

```bash
# Test the chain architecture
python test_chain_architecture.py
```

## 📋 Current Status

✅ **Completed (Task 1 - Core Architecture)**:
- Core chain-of-responsibility architecture
- Base handler interface and context classes
- Configuration management system
- Client configuration validation
- Command-line interface
- Architecture testing

✅ **Completed (Task 2 - Data Fetcher Handler)**:
- Base DataFetcherHandler with URL extraction and platform detection
- DingTalkDataFetcher for 钉钉多维表格 integration
- ServiceClientDataFetcher for API service integration
- DataFetcherFactory for creating appropriate fetchers
- Comprehensive filtering and validation logic
- Custom data fetcher support and examples

✅ **Completed (Task 3 - Work Updater Handler)**:
- Base WorkUpdaterHandler with platform integration
- DatabaseWorkUpdater for database storage compatibility
- Support for async/sync platform handlers (小红书, 抖音, 快手, 微信视频号)
- WorkUpdaterFactory for creating appropriate updaters
- Batch processing with configurable batch sizes
- Comprehensive error handling and retry logic
- Custom work updater support and examples

✅ **Completed (Task 4 - Data Sync Handler)**:
- Base DataSyncHandler with flexible sync architecture
- DingTalkDataSync for 钉钉多维表格 direct API integration
- WebhookDataSync for legacy webhook-based synchronization
- DataSyncFactory for creating appropriate sync handlers
- Configurable field mapping and data transformation
- Client-specific field mapping (水云兔, 淘天, 佳尔优优, 朱栈科技)
- Batch synchronization with error handling
- Custom data sync handler support and examples

✅ **Completed (Task 5 - Notification Handler)**:
- Base NotificationHandler with threshold-based alerting
- DingTalkNotification for 钉钉消息通知 integration
- EmailNotification for SMTP email notifications
- DatabaseDingTalkNotification with notification history tracking
- NotificationFactory and MultiChannelNotification support
- Configurable threshold rules and message templates
- Client-specific threshold rules (水云兔, 淘天)
- Multi-recipient and multi-channel notification support
- Custom notification handler support and examples

✅ **Completed (Task 6 - Simplified Client Extensions)**:
- Simplified factory pattern with CoreHandlerFactory
- Direct inheritance approach for client customizations
- Client-specific chain builders (Shuiyuntu example)
- Template-based client extension framework
- Backward compatibility with existing factories
- Comprehensive usage examples and documentation

🚧 **In Progress**:
- Performance optimizations
- Production deployment guides

## 🔧 客户扩展指南

### 方式1：直接继承（推荐）
```python
from core.handlers import WorkUpdaterHandler, create_data_fetcher, create_data_sync

class MyCustomWorkUpdater(WorkUpdaterHandler):
    def _process_single_task(self, context, task):
        # 添加自定义逻辑
        result = super()._process_single_task(context, task)
        # 自定义后处理
        return result

# 构建自定义链
def build_my_chain(config, logger):
    data_fetcher = create_data_fetcher(config, logger)
    work_updater = MyCustomWorkUpdater(config, logger)  # 直接实例化
    data_sync = create_data_sync(config, logger)

    return data_fetcher.set_next(work_updater).set_next(data_sync)
```

### 方式2：使用便利函数
```python
from core.handlers import DefaultChainBuilder

# 快速创建标准链
chain = DefaultChainBuilder.build_standard_chain(config, logger)
```

### 方式3：客户特定构建器
```python
class MyClientChainBuilder:
    @staticmethod
    def build_production_chain(config, logger):
        # 自定义生产环境链
        pass

    @staticmethod
    def build_testing_chain(config, logger):
        # 自定义测试环境链
        pass
```

### 示例文件
- `clients/shuiyuntu/` - 水云兔客户扩展示例
- `clients/template/` - 客户扩展模板
- `examples/simplified_usage_example.py` - 详细使用示例

## 🔄 Development Workflow

1. **Add New Client**: Use `python -m main create-config <client-name>`
2. **Customize Handlers**: Inherit from base handlers and override specific methods
3. **Configure**: Edit the generated YAML configuration file
4. **Validate**: Run `python -m main validate --client <client-name>`
5. **Test**: Create client-specific test cases