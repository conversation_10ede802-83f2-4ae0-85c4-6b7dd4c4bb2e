"""
通知逻辑Handler - 负责基于阈值规则发送通知
"""

import logging
import time
from typing import List, Dict, Any
from abc import ABC, abstractmethod

from .base_handler import BaseHandler
from config.client_config import ClientConfig
from ..pipeline_context import PipelineContext
from ..exceptions import NotificationException
from models.work_models import WorkDetailTaskModel


class NotifyHandler(BaseHandler):
    """
    通知逻辑处理器基类

    职责：
    - 检查作品是否达到通知阈值
    - 发送通知给相关人员
    - 记录通知历史
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.logger = logger.getChild(self.__class__.__name__)
        self.notification_records = set()  # 简单的内存存储

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理通知逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"Starting notification process for {len(context.task_list)} works")

            notification_results = []
            notifications_sent = 0

            for work in context.task_list:
                try:
                    result = self._process_work_notification(work, context)
                    notification_results.append(result)

                    if result.get("sent", False):
                        notifications_sent += 1

                except Exception as e:
                    self.logger.error(f"Failed to process notification for {work.work_id}: {e}")
                    notification_results.append(
                        {"work_id": work.work_id, "success": False, "sent": False, "error": str(e)}
                    )

            # 记录通知结果（简化版本，只记录发送数量）
            # 可以在这里添加更多的通知统计逻辑

            self.logger.info(
                f"Notification process completed: {notifications_sent} notifications sent"
            )

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Notification process failed: {e}")
            raise NotificationException(f"Failed to process notifications: {str(e)}")

    def _process_work_notification(
        self, work: WorkDetailTaskModel, context: PipelineContext
    ) -> Dict[str, Any]:
        """
        处理单个作品的通知

        Args:
            work: 作品信息
            context: 工作上下文

        Returns:
            通知处理结果
        """
        try:
            # 检查是否需要通知
            if not self._should_notify(work):
                return {
                    "work_id": work.work_id,
                    "success": True,
                    "sent": False,
                    "reason": "threshold_not_met",
                }

            # 检查是否已经通知过
            if self._is_already_notified(work):
                return {
                    "work_id": work.work_id,
                    "success": True,
                    "sent": False,
                    "reason": "already_notified",
                }

            # 获取通知接收人
            recipients = self._get_recipients(work)
            if not recipients:
                return {
                    "work_id": work.work_id,
                    "success": True,
                    "sent": False,
                    "reason": "no_recipients",
                }

            # 构建通知消息
            message = self._build_notification_message(work)

            # 发送通知
            send_result = self._send_notification(work, recipients, message)

            # 记录通知
            if send_result:
                self._record_notification(work)

            return {
                "work_id": work.work_id,
                "success": True,
                "sent": send_result,
                "recipients_count": len(recipients),
                "message": message,
            }

        except Exception as e:
            self.logger.error(f"Failed to process notification for work {work.work_id}: {e}")
            return {"work_id": work.work_id, "success": False, "sent": False, "error": str(e)}

    def _should_notify(self, work: WorkDetailTaskModel) -> bool:
        """
        检查是否应该发送通知

        Args:
            work: 作品信息

        Returns:
            是否应该通知
        """
        # 检查是否有作品详情
        if not work.author_work:
            return False

        # 检查阈值标记
        if work.threshold != "是":
            return False

        # 检查具体阈值
        return self._check_threshold_details(work)

    def _check_threshold_details(self, work: WorkDetailTaskModel) -> bool:
        """检查具体阈值"""
        try:
            threshold_config = self._get_threshold_config()
            author_work = work.author_work

            like_count = getattr(author_work, "like_count", 0)
            comment_count = getattr(author_work, "comment_count", 0)
            share_count = getattr(author_work, "share_count", 0)

            return (
                like_count >= threshold_config.get("like_count", float("inf"))
                or comment_count >= threshold_config.get("comment_count", float("inf"))
                or share_count >= threshold_config.get("share_count", float("inf"))
            )

        except Exception as e:
            self.logger.warning(f"Failed to check threshold details: {e}")
            return False

    def _is_already_notified(self, work: WorkDetailTaskModel) -> bool:
        """检查是否已经通知过"""
        notification_key = f"{work.work_id}_{work.platform}_{work.update_count}"
        return notification_key in self.notification_records

    def _get_recipients(self, work: WorkDetailTaskModel) -> List[Dict[str, Any]]:
        """获取通知接收人"""
        recipients = []

        # 从作品提交用户获取
        if work.submit_user:
            for user in work.submit_user:
                if isinstance(user, dict) and user.get("unionId"):
                    recipients.append(user)

        # 添加默认接收人
        notification_config = self.client_config.notification_config or {}
        default_recipients = notification_config.get("default_recipients", [])
        for recipient in default_recipients:
            if recipient not in recipients:
                recipients.append(recipient)

        return recipients

    def _build_notification_message(self, work: WorkDetailTaskModel) -> str:
        """构建通知消息"""
        if not work.author_work:
            return f"🚨 作品阈值通知：{work.work_url}"

        author_work = work.author_work

        # 获取消息模板
        template = self._get_message_template()

        # 构建消息
        message = template.format(
            title=getattr(author_work, "title", "未知"),
            author_name=getattr(author_work, "author_name", "未知"),
            platform=self._get_platform_name(work.platform),
            like_count=getattr(author_work, "like_count", 0),
            comment_count=getattr(author_work, "comment_count", 0),
            share_count=getattr(author_work, "share_count", 0),
            work_url=work.work_url,
            threshold_info=self._get_threshold_info(work),
        )

        return message

    def _get_message_template(self) -> str:
        """获取消息模板"""
        notification_config = self.client_config.notification_config or {}
        templates = notification_config.get("message_templates", {})

        # 优先使用客户特定模板
        client_template = templates.get(self.client_config.name)
        if client_template:
            return client_template

        # 使用默认模板
        return templates.get(
            "default",
            "🚨 作品阈值通知：标题={title}, 作者={author_name}, 平台={platform}, "
            "点赞={like_count}, 评论={comment_count}, 分享={share_count}, "
            "作品链接={work_url}",
        )

    def _get_platform_name(self, platform: str) -> str:
        """获取平台显示名称"""
        platform_names = {"xhs": "小红书", "dy": "抖音", "ks": "快手", "wxvideo": "微信视频号"}
        return platform_names.get(platform, platform)

    def _get_threshold_info(self, work: WorkDetailTaskModel) -> str:
        """获取阈值触发信息"""
        if not work.author_work:
            return ""

        threshold_config = self._get_threshold_config()
        author_work = work.author_work

        triggered = []

        like_count = getattr(author_work, "like_count", 0)
        if like_count >= threshold_config.get("like_count", float("inf")):
            triggered.append(f"点赞数达到{like_count}")

        comment_count = getattr(author_work, "comment_count", 0)
        if comment_count >= threshold_config.get("comment_count", float("inf")):
            triggered.append(f"评论数达到{comment_count}")

        share_count = getattr(author_work, "share_count", 0)
        if share_count >= threshold_config.get("share_count", float("inf")):
            triggered.append(f"分享数达到{share_count}")

        return ", ".join(triggered)

    def _get_threshold_config(self) -> Dict[str, int]:
        """获取阈值配置"""
        notification_config = self.client_config.notification_config or {}
        threshold_rules = notification_config.get("threshold_rules", {})

        # 优先使用客户特定的阈值
        client_threshold = threshold_rules.get(self.client_config.name)
        if client_threshold:
            return client_threshold

        # 使用默认阈值
        return threshold_rules.get(
            "default", {"like_count": 100, "comment_count": 30, "share_count": 20}
        )

    @abstractmethod
    def _send_notification(
        self, work: WorkDetailTaskModel, recipients: List[Dict[str, Any]], message: str
    ) -> bool:
        """
        发送通知 - 子类必须实现

        Args:
            work: 作品信息
            recipients: 接收人列表
            message: 通知消息

        Returns:
            是否发送成功
        """
        pass

    def _record_notification(self, work: WorkDetailTaskModel):
        """记录通知"""
        notification_key = f"{work.work_id}_{work.platform}_{work.update_count}"
        self.notification_records.add(notification_key)


class DingTalkNotifyHandler(NotifyHandler):
    """
    钉钉通知处理器
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self._dingtalk_client = None

    def _send_notification(
        self, work: WorkDetailTaskModel, recipients: List[Dict[str, Any]], message: str
    ) -> bool:
        """发送钉钉通知"""
        try:
            dingtalk_client = self._get_dingtalk_client()

            success_count = 0
            for recipient in recipients:
                union_id = recipient.get("unionId")
                if union_id:
                    try:
                        result = dingtalk_client.send_message(union_id, message)
                        if result:
                            success_count += 1
                            self.logger.debug(f"Sent notification to {union_id}")
                    except Exception as e:
                        self.logger.warning(f"Failed to send to {union_id}: {e}")

            return success_count > 0

        except Exception as e:
            self.logger.error(f"Failed to send DingTalk notifications: {e}")
            return False

    def _get_dingtalk_client(self):
        """获取钉钉客户端"""
        if self._dingtalk_client is None:
            try:
                from jss_api_extend.dingtalk_bitable_factory import DingtalkBitableFactory

                self._dingtalk_client = DingtalkBitableFactory.create_dingtalk_client(
                    api_token=self.client_config.user_info.get("api-token")
                )

            except ImportError as e:
                self.logger.error(f"Failed to import DingTalk dependencies: {e}")
                raise NotificationException("DingTalk dependencies not available")
            except Exception as e:
                self.logger.error(f"Failed to create DingTalk client: {e}")
                raise NotificationException(f"DingTalk client creation failed: {str(e)}")

        return self._dingtalk_client
