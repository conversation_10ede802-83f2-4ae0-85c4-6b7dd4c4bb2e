# -*- coding: utf-8 -*-

# @Time : 2025/6/13 18:24 
# <AUTHOR> cyf
# @File : url_utils.py

import re
from typing import Optional
import requests
from urllib.parse import urlparse, parse_qs


class UrlUtils:
    @staticmethod
    def get_xhs_work_id(url: str) -> Optional[str]:
        """
        提取小红书笔记ID

        此函数通过正则表达式查找URL中符合小红书笔记ID特征的字符串（24位十六进制字符），
        能够兼容例如 /discovery/item/, /explore/ 等路径格式，以及包含 target_note_id 的查询参数格式。

        @param url: 小红书笔记的URL链接
        @return: 提取到的24位笔记ID，如果未找到则返回None
        """
        if not url:
            return None

        # 1. 优先从URL路径中查找ID (e.g., /explore/6853c28e...)
        path_part = url.split('?')[0]
        path_match = re.search(r'/([0-9a-f]{24})', path_part)
        if path_match:
            return path_match.group(1)

        # 2. 【新增】如果路径中没有，则查找 target_note_id 查询参数
        # (e.g., ?...&target_note_id=6853c28e...&...)
        query_match = re.search(r'target_note_id=([0-9a-f]{24})', url)
        if query_match:
            return query_match.group(1)

        # 3. 作为最后的备用方案，在整个URL中模糊搜索第一个出现的24位十六进制字符串
        # (这可以覆盖一些未知的URL格式，也能处理您提供的新链接，但没有第2步明确)
        fallback_match = re.search(r'([0-9a-f]{24})', url)
        if fallback_match:
            return fallback_match.group(1)

        return None

    @staticmethod
    def get_dy_work_id(url: str) -> Optional[str]:
        """
        提取抖音
        @param url:
        @return:
        """
        if not url:
            return None

        # Match continuous digits with length of at least 10
        pattern = re.compile(r'\d{10,}')
        match = pattern.search(url)

        if match:
            return match.group()

        return None

    @staticmethod
    def check_short_url(work_url: str) -> bool:
        if work_url.startswith("https://v.douyin.com/") \
                or work_url.startswith("http://xhslink.com/"):
            return True
        return False

    @staticmethod
    def select_platform(work_url: str) -> Optional[str]:
        """
        选择平台 抖音分享链接和主站链接
        @param work_url:
        @return:
        """
        if work_url.startswith("https://www.douyin.com/") or \
                work_url.startswith("https://www.iesdouyin.com/"):
            return "dy"
        elif work_url.startswith("https://www.xiaohongshu.com/"):
            return "xhs"
        else:
            return None

    def get_kuaishou_work_id(url, resolve_short_url=True):
        """
        从所有格式的快手视频链接中提取视频ID (photoId)

        参数:
            url (str): 快手视频链接
            resolve_short_url (bool): 是否解析短链接（默认True）

        返回:
            str: 视频ID，如果未找到则返回None
        """
        # 1. 处理短链接重定向
        if resolve_short_url and "v.kuaishou.com" in url:
            try:
                response = requests.head(url, allow_redirects=True, timeout=5)
                url = response.url
            except:
                pass

        # 2. 解析URL
        parsed = urlparse(url)

        # 3. 从路径中提取视频ID（适用于大多数情况）
        path_segments = [seg for seg in parsed.path.split('/') if seg]

        # 快手视频ID的常见位置模式
        patterns = [
            # 标准格式: /short-video/{photoId}
            r"short-video/([a-z0-9]+)$",
            # 标准格式: /video/{photoId}
            r"video/([a-z0-9]+)$",
            # 用户分享格式: /u/{userId}/{photoId}
            r"u/[a-z0-9]+/([a-z0-9]+)$",
            # 移动端格式: /short-video/{photoId}
            r"short-video/([a-z0-9]+)$",
            # 直播回放格式: /play/{photoId}
            r"play/([a-z0-9]+)$",
            # 新格式: /f/{photoId}
            r"f/([a-z0-9]+)$",
        ]

        # 尝试匹配路径模式
        for pattern in patterns:
            match = re.search(pattern, parsed.path)
            if match:
                return match.group(1)

        # 4. 尝试从查询参数中提取（某些特殊格式）
        query_params = parse_qs(parsed.query)
        for key in ['photoId', 'vid', 'videoId']:
            if key in query_params:
                return query_params[key][0]

        # 5. 尝试从最后一段路径提取（保底方法）
        if path_segments:
            last_segment = path_segments[-1]
            # 检查是否符合视频ID格式（字母数字组合，通常长度12-20）
            if re.match(r"^[a-z0-9]{10,25}$", last_segment):
                return last_segment

        return None


if __name__ == '__main__':
    print(UrlUtils.select_platform("https://www.iesdouyin.com/share/video/7519438958846938422/?region=US&mid=7519438700648925963&u_code=-1&did=MS4wLjABAAAAYXb9Wtyfnj6jtWYrTA13wSnCeIXeS9k0zmvf6E6y7zmrk4i8bfyXodvtMSK8VjyC&iid=MS4wLjABAAAAaMytay1a88FRcpWmE4-EwTQE5A3pBASE3aymknqw-j3c_j3fssXskEX1DUrqh12B&with_sec_did=1&video_share_track_ver=&titleType=title&share_sign=7u6xgp69T43OLZwdITlKw3OLx4E4YAeizAy2UpD1sms-&share_version=270500&ts=1751008200&from_aid=1128&from_ssr=1"))

