# -*- coding: utf-8 -*-

import urllib
from urllib.parse import urlparse

import pymysql
from dbutils.pooled_db import PooledDB


class MysqlDataSource(object):
    """
    mysql数据库操作类
    """

    def __init__(self, url, logger):
        self.logger = logger

        url = urlparse(url)

        self._pool = PooledDB(
            creator=pymysql,
            maxconnections=50,  # 连接池的最大连接数
            maxcached=10,
            maxshared=10,
            blocking=True,
            setsession=[],
            host=url.hostname,
            port=url.port or 3306,
            user=url.username,
            password=urllib.parse.unquote(url.password),
            database=url.path.strip().strip('/'),
            charset='utf8mb4',
        )

    def connect(self):
        conn = self._pool.connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        return conn, cursor

    def connect_close(self, conn, cursor):
        cursor.close()
        conn.close()

    def fetch_all(self, sql, args):
        conn, cursor = self.connect()
        if args is None:
            cursor.execute(sql)
        else:
            cursor.execute(sql, args)

        self.connect_close(conn, cursor)

        record_list = cursor.fetchall()
        return record_list

    def fetch_one(self, sql, args):
        conn, cursor = self.connect()
        cursor.execute(sql, args)
        result = cursor.fetchone()

        self.connect_close(conn, cursor)

        return result

    def insert(self, sql, args):
        conn, cursor = self.connect()
        _row = 0
        try:
            _row = cursor.execute(sql, args)
            conn.commit()
        except Exception as e:
            self.logger.error("insert db error, %s", e)

        self.connect_close(conn, cursor)
        return _row

    def execute(self, sql, args=None):
        conn, cursor = self.connect()
        _row = 0
        try:
            _row = cursor.execute(sql, args)
            conn.commit()
        except Exception as e:
            self.logger.error("insert db error, %s", e)

        self.connect_close(conn, cursor)
        return _row

    def batch_insert(self, sql, args_tuple):
        conn, cursor = self.connect()
        _row = 0
        try:
            _row = cursor.executemany(sql, args_tuple)
            conn.commit()
        except Exception as e:
            self.logger.error("insert db error, %s", e)

        self.connect_close(conn, cursor)
        return _row

    def is_table_exist(self, table_name):
        conn, cursor = self.connect()
        cursor.execute('show tables like %s', table_name)
        result = cursor.fetchone()

        self.connect_close(conn, cursor)

        if result is None:
            return False

        if len(result) > 0:
            return True
