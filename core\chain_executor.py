"""
Chain executor for managing the execution of handler chains.
"""

import logging
from typing import Optional, List
from datetime import datetime

from config.client_config import ClientConfig
from .pipeline_context import PipelineContext
from .handlers.base_handler import <PERSON>Handler
from .exceptions import WorkMonitorException
from utils.time_utils import TimeUtils


class ChainExecutor:
    """
    Manages the execution of handler chains for work monitoring.

    This class is responsible for:
    - Setting up the execution context
    - Running the handler chain
    - Collecting and reporting execution metrics
    - Handling errors and cleanup
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        """
        Initialize the chain executor.

        Args:
            client_config: Configuration for the specific client
            logger: Logger instance for this executor
        """
        self.client_config = client_config
        self.logger = logger
        self.chain_head: Optional[BaseHandler] = None

    def set_chain(self, chain_head: BaseHandler) -> None:
        """
        Set the head of the handler chain.

        Args:
            chain_head: The first handler in the chain
        """
        self.chain_head = chain_head

    def execute(self, batch_id: Optional[str] = None) -> PipelineContext:
        """
        Execute the complete handler chain.

        Args:
            batch_id: Optional batch ID, will be generated if not provided

        Returns:
            The final pipeline context with execution results

        Raises:
            WorkMonitorException: If execution fails
        """
        if not self.chain_head:
            raise WorkMonitorException("No handler chain configured")

        # Generate batch ID if not provided
        if not batch_id:
            batch_id = TimeUtils.get_current_ts("%Y%m%d%H%M%S")

        # Create execution context
        context = PipelineContext(
            client_name=self.client_config.name, batch_id=batch_id, client_config=self.client_config
        )

        self.logger.info(
            f"Starting chain execution for client: {self.client_config.name}, batch: {batch_id}"
        )

        try:
            # Execute the handler chain
            success = self._execute_chain(context)

            # Log execution summary
            self._log_execution_summary(context, success)

            return context

        except Exception as e:
            self.logger.error(f"Chain execution failed: {str(e)}")
            context.add_error(f"Chain execution failed: {str(e)}")
            raise WorkMonitorException(
                f"Chain execution failed for client {self.client_config.name}",
                {"batch_id": batch_id, "error": str(e), "context_summary": context.to_summary()},
            )

    def _execute_chain(self, context: PipelineContext) -> bool:
        """
        Execute the handler chain starting from the head.

        Args:
            context: The pipeline context to process

        Returns:
            True if the entire chain executed successfully
        """
        current_handler = self.chain_head
        overall_success = True

        while current_handler:
            try:
                # Execute current handler with logging
                context = current_handler.execute_with_logging(context)

                # Move to next handler
                current_handler = current_handler.next_handler

            except Exception as e:
                self.logger.error(
                    f"Unexpected error in handler {current_handler.handler_name}: {str(e)}"
                )
                context.add_error(f"Handler {current_handler.handler_name} failed: {str(e)}")
                overall_success = False
                break

        return overall_success

    def _log_execution_summary(self, context: PipelineContext, success: bool) -> None:
        """
        Log a summary of the execution results.

        Args:
            context: The final pipeline context
            success: Whether the execution was successful
        """
        summary = context.to_summary()

        self.logger.info("=" * 60)
        self.logger.info(f"EXECUTION SUMMARY - Client: {context.client_name}")
        self.logger.info("=" * 60)
        self.logger.info(f"Batch ID: {context.batch_id}")
        self.logger.info(f"Execution Time: {summary['execution_time']:.2f} seconds")
        self.logger.info(f"Overall Success: {success}")
        self.logger.info(f"Total Processed: {summary['total_processed']}")
        self.logger.info(f"Successful Updates: {summary['successful_updates']}")
        self.logger.info(f"Failed Updates: {summary['failed_updates']}")
        self.logger.info(f"Success Rate: {summary['success_rate']:.1f}%")
        self.logger.info(f"Notifications Sent: {summary['notifications_sent']}")
        self.logger.info(f"Failed URLs: {summary['failed_urls_count']}")
        self.logger.info(f"Errors: {summary['error_count']}")

        if context.error_messages:
            self.logger.info("Error Messages:")
            for i, error in enumerate(context.error_messages, 1):
                self.logger.info(f"  {i}. {error}")

        if context.failed_urls:
            self.logger.info("Failed URLs:")
            for i, url in enumerate(context.failed_urls, 1):
                self.logger.info(f"  {i}. {url}")

        self.logger.info("=" * 60)

    def validate_chain(self) -> List[str]:
        """
        Validate the handler chain configuration.

        Returns:
            List of validation errors (empty if valid)
        """
        errors = []

        if not self.chain_head:
            errors.append("No handler chain configured")
            return errors

        # Check for circular references
        visited_handlers = set()
        current_handler = self.chain_head

        while current_handler:
            handler_id = id(current_handler)
            if handler_id in visited_handlers:
                errors.append("Circular reference detected in handler chain")
                break

            visited_handlers.add(handler_id)
            current_handler = current_handler.next_handler

        return errors
