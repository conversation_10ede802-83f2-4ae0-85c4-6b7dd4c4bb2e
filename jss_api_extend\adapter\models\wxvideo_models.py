from typing import Dict

class WxVideoModel:
    def __init__(self,
                 object_id,
                 object_nonce_id,
                 media_type,
                 live_info,
                 fav_count,
                 like_count,
                 forward_count,
                 comment_count,
                 title,
                 download_url,
                 publish_time,
                 file_size,
                 video_play_len,
                 cover_url,
                 export_id
                 ):
        self.object_id = object_id
        self.object_nonce_id = object_nonce_id
        self.media_type = media_type
        self.live_info = live_info
        self.fav_count = fav_count
        self.like_count = like_count
        self.forward_count = forward_count
        self.comment_count = comment_count
        self.title = title
        self.download_url = download_url
        self.publish_time = publish_time
        self.file_size = file_size
        self.video_play_len = video_play_len
        self.cover_url = cover_url
        self.export_id = export_id



