# -*- coding: utf-8 -*-


class AuthorWorkComment(object):
    def __init__(self,
                 work_url,
                 platform,
                 comment_uuid,
                 commenter_name,
                 commenter_id,
                 commenter_url,
                 content,
                 publish_time,
                 like_count,
                 location_ip,
                 first_cid: str,
                 has_sub=0,
                 is_sub=0,
                 sec_uid="",
                 news_parent_id=""):
        self.work_url = work_url
        self.platform = platform
        self.comment_uuid = comment_uuid
        self.commenter_name = commenter_name
        self.commenter_id = commenter_id
        self.commenter_url = commenter_url
        self.content = content
        self.publish_time = publish_time
        self.like_count = like_count
        self.location_ip = location_ip
        self.first_cid = first_cid
        self.has_sub = has_sub
        self.is_sub = is_sub
        self.sec_uid = sec_uid
        self.news_parent_id = news_parent_id
        self.sub_comments = []
