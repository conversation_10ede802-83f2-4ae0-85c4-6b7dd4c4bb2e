# -*- coding: utf-8 -*-

from dao.db_adapter import g_balu_database_product
from dao.model.author_work import AuthorWork
from utils.time_utils import TimeUtils


class BaluXhsWorkDetailRecordRepository(object):
    """
    对应表格 bardata 操作
    """

    def __init__(self):
        self.gdb_datasource = g_balu_database_product

    def insert(self, author_work_record: AuthorWork):
        d = author_work_record.title + ", " + author_work_record.content
        if "巴奴" not in d:
            return

        sql = '''
            INSERT INTO `jss_xhs`(
                `jss_id`, `platform`, `author_id`, 
                `author_identity`,  `author_avatar`, 
                `author_name`, `author_url`, 
                `work_id`, `work_uuid`, `url`, 
                `download_url`, `digest`, 
                `title`, `thumbnail_link`, `content`, 
                `img_urls`, `video_urls`, `publish_time`, 
                `read_count`, `like_count`, `comment_count`, 
                `share_count`, `collect_count`, `catch_date`) 
            VALUES (%s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s);'''

        args = ("", 'xhs', author_work_record.author_id, author_work_record.author_identity,
                author_work_record.author_avatar, author_work_record.author_name, author_work_record.author_url,
                author_work_record.work_id, author_work_record.work_uuid, author_work_record.url,
                author_work_record.download_url, author_work_record.digest,
                author_work_record.title, author_work_record.thumbnail_link, author_work_record.content,
                author_work_record.img_urls, author_work_record.video_urls,  author_work_record.publish_time,
                author_work_record.read_count, author_work_record.like_count, author_work_record.comment_count,
                author_work_record.share_count, author_work_record.collect_count, TimeUtils.get_current_ts())

        return self.gdb_datasource.insert(sql=sql, args=args)

    def query_work_detail(self, work_id):
        sql = 'select * from jss_xhs where work_id=%s'
        return self.gdb_datasource.fetch_all(sql, (work_id))

    def update_work_detail(self, work_id, image_url):
        sql = 'update jss_xhs ' \
              'set `img_urls`=%s where work_id=%s'
        return self.gdb_datasource.execute(sql, (image_url, work_id))
