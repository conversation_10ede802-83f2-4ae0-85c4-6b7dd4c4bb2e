# -*- encoding:utf-8 -*-
import base64
import hashlib
import uuid
from typing import Optional


class UUIDUtils:
    @staticmethod
    def generate_order_id():
        uuid_str = str(uuid.uuid1())

        md5 = hashlib.md5()
        md5.update(uuid_str.encode('utf-8'))

        return md5.hexdigest()

    # @staticmethod
    # def generate_comment_uuid(key1, key2, key3):
    #     uuid_str = key1 + key2 + key3
    #
    #     md5 = hashlib.md5()
    #     md5.update(uuid_str.encode('utf-8'))
    #
    #     return md5.hexdigest()

    @staticmethod
    def generate_comment_uuid(comment_user: str,
                              comment_content: str,
                              time: str) -> Optional[str]:
        """
        使用 SHA-256 哈希评论信息并返回 Base64 编码的字符串。

        此函数复制了原始 Java 方法的行为。

        Args:
            comment_user: 评论用户的名称。
            comment_content: 评论的内容。
            time: 评论的时间戳。

        Returns:
            一个 Base64 编码的 SHA-256 哈希字符串，如果发生错误则返回 None。
        """
        try:
            input_string = f"{comment_user}:{comment_content}:{time}"
            input_bytes = input_string.encode('utf-8')
            hash_bytes = hashlib.sha256(input_bytes).digest()
            base64_encoded_hash = base64.b64encode(hash_bytes)
            return base64_encoded_hash.decode('utf-8')
        except Exception:
            return None


if __name__ == '__main__':
    print(UUIDUtils.generate_comment_uuid("5e5a48ab0000000001005f47", "码住", "2025-06-18 17:13:07"))
