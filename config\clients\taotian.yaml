# 淘天客户配置
name: "淘天"

# 调度配置
schedule:
  cron: "0 */3 * * *"  # 每3小时执行一次
  max_tasks: 150

# 数据获取配置
data_fetcher:
  source_type: "dingtalk_bitable"  # 支持: dingtalk_bitable, service_client
  filter_rules:
    update_times_limit: 10  # 最大更新次数限制
    platform: "all"  # 平台过滤: all, xhs, dy 等

# 作品更新配置
work_updater:
  platforms: ["xhs", "dy"]  # 支持的平台：xhs(小红书), dy(抖音), ks(快手), wxvideo(微信视频号)
  batch_size: 100  # 批处理大小
  updater_type: "database"  # 更新器类型：basic, database

# 数据同步配置
data_sync:
  sync_type: "webhook"  # 同步类型：webhook, dingtalk_bitable
  batch_size: 30  # 同步批处理大小
  field_mapping:  # 字段映射配置
    "作品标题": "title"
    "平台": "platform_name"
    "作者": "author_name"
    "作者主页": "author_url"
    "正文": "content"
    "作品发布时间": "publish_time"
    "点赞数": "like_count"
    "评论数": "comment_count"
    "分享数": "share_count"
    "收藏数": "collect_count"
    "更新次数": "update_count"
    "是否触发阈值": "threshold"
    "更新时间": "record_time"

# 通知配置
notification:
  enabled_channels: ["dingtalk"]  # 启用的通知渠道：dingtalk, email
  use_database: true  # 是否使用数据库记录通知历史
  threshold_rules:  # 阈值规则配置
    "淘天":
      like_count: 150    # 点赞数阈值
      comment_count: 40  # 评论数阈值
      share_count: 25    # 分享数阈值
    default:
      like_count: 200
      comment_count: 50
      share_count: 30
  message_templates:  # 消息模板配置
    "淘天": "🚨 作品阈值通知：标题={title}, 作者={author_name}, 平台={platform}, {threshold_info}, 作品链接={work_url}"
    default: "作品阈值通知：{title} - {author_name} ({platform})"
  default_recipients:  # 默认通知接收人
    - unionId: "admin-taotian-1"
    - unionId: "admin-taotian-2"

# 数据同步配置
data_sync:
  field_mapping:
    "作品标题": "title"
    "平台": "platform"
    "作者": "author_name"
    "作者主页": "author_url"
    "正文": "content"
    "作品发布时间": "publish_time"
    "点赞数": "like_count"
    "评论数": "comment_count"
    "分享数": "share_count"
    "收藏数": "collect_count"
    "更新次数": "update_count"
    "是否触发阈值": "threshold"
    "更新时间": "record_time"
  batch_size: 100

# 通知配置 - 淘天有特殊的阈值规则
notification:
  threshold:
    like_count: 50
    comment_count: 50
    share_count: 50
    collect_count: 50
  message_template: "1688舆情小助手-风险发酵提醒：工单编号{extends}，转赞评已达到阈值，请及时处理，风险链接：{work_url}@运营负责人"
  channels: ["dingtalk"]

# 用户信息配置
user_info:
  api-token: "your-api-token-here"
  user_id: 10
  tenant: "your-tenant"

# 多维表格配置
bitable:
  webhook: "your-webhook-url"
  dentryUuid: "your-dentry-uuid"
  idOrName: "基础数据表"
