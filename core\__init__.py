"""
Core module for the work monitor system.

This module contains the core components of the chain-of-responsibility
architecture including handlers, context, and execution management.
"""

from config.client_config import ClientConfig
from .pipeline_context import PipelineContext
from .exceptions import WorkMonitorException, HandlerException
from .handlers.base_handler import BaseHandler
from .chain_executor import ChainExecutor

__all__ = [
    "PipelineContext",
    "ClientConfig",
    "WorkMonitorException",
    "HandlerException",
    "BaseHandler",
    "ChainExecutor",
]
