"""
Base configuration classes for the work monitor system.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path


class BaseConfig:
    """
    Base configuration class with common settings.
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.log_dir = self.project_root / "logs"
        self.data_dir = self.project_root / "data"
        
        # Ensure directories exist
        self.log_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        (self.config_dir / "clients").mkdir(exist_ok=True)
    
    @property
    def database_url(self) -> str:
        """Get database URL from environment or default."""
        return os.getenv('DATABASE_URL', 'sqlite:///work_monitor.db')
    
    @property
    def redis_url(self) -> str:
        """Get Redis URL from environment or default."""
        return os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    @property
    def log_level(self) -> str:
        """Get log level from environment or default."""
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def environment(self) -> str:
        """Get environment from environment variable or default."""
        return os.getenv('ENVIRONMENT', 'development')
    
    def get_client_config_path(self, client_name: str) -> Path:
        """
        Get the configuration file path for a specific client.
        
        Args:
            client_name: Name of the client
            
        Returns:
            Path to the client configuration file
        """
        yaml_path = self.config_dir / "clients" / f"{client_name}.yaml"
        json_path = self.config_dir / "clients" / f"{client_name}.json"
        
        if yaml_path.exists():
            return yaml_path
        elif json_path.exists():
            return json_path
        else:
            return yaml_path  # Return YAML path as default for creation
    
    def get_log_file_path(self, client_name: str) -> Path:
        """
        Get the log file path for a specific client.
        
        Args:
            client_name: Name of the client
            
        Returns:
            Path to the client log file
        """
        return self.log_dir / f"{client_name}.log"
    
    def get_common_log_file_path(self) -> Path:
        """
        Get the common log file path.
        
        Returns:
            Path to the common log file
        """
        return self.log_dir / "work_monitor.log"
