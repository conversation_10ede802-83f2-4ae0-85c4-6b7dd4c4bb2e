"""
简化的4个核心Handler工厂和链构建器
"""

import logging
from ..context import ClientConfig

# Import 4 core handlers
from .data_handler import DataHandler, DingTalkDataHandler
from .update_handler import UpdateHandler, DatabaseUpdateHandler
from .notify_handler import NotifyHandler, DingTalkNotifyHandler
from .sync_handler import SyncHandler, DingTalkSyncHandler, WebhookSyncHandler


class CoreHandlerFactory:
    """
    4个核心Handler的工厂类
    
    提供4个核心处理器的创建方法：
    1. 数据获取Handler
    2. 更新逻辑Handler  
    3. 通知逻辑Handler
    4. 数据同步Handler
    """
    
    # 1. 数据获取Handler - 客户必须明确指定数据源类型
    @staticmethod
    def create_server_data_handler(config: ClientConfig, logger: logging.Logger) -> 'ServerDataHandler':
        """
        创建服务器数据获取Handler

        从服务器查询数据，需要客户的 api_token
        参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法实现

        Args:
            config: 客户配置（必须包含 api-token）
            logger: 日志记录器

        Returns:
            服务器数据获取Handler实例
        """
        from .data_handler import ServerDataHandler
        return ServerDataHandler(config, logger)

    @staticmethod
    def create_bitable_data_handler(config: ClientConfig, logger: logging.Logger) -> 'BitableDataHandler':
        """
        创建多维表格数据获取Handler

        从钉钉多维表格获取数据，使用 jss-api-extend
        每个客户的条件不同，建议继承实现自定义逻辑

        Args:
            config: 客户配置（必须包含 bitable 配置）
            logger: 日志记录器

        Returns:
            多维表格数据获取Handler实例
        """
        from .data_handler import BitableDataHandler
        return BitableDataHandler(config, logger)

    @staticmethod
    def create_dingtalk_data_handler(config: ClientConfig, logger: logging.Logger) -> DingTalkDataHandler:
        """创建钉钉数据获取处理器（别名，等同于 create_bitable_data_handler）"""
        return DingTalkDataHandler(config, logger)
    
    # 2. 更新逻辑Handler
    @staticmethod
    def create_update_handler(config: ClientConfig, logger: logging.Logger) -> UpdateHandler:
        """创建基础更新处理器"""
        return UpdateHandler(config, logger)
    
    @staticmethod
    def create_database_update_handler(config: ClientConfig, logger: logging.Logger) -> DatabaseUpdateHandler:
        """创建数据库更新处理器"""
        return DatabaseUpdateHandler(config, logger)
    
    # 3. 通知逻辑Handler
    @staticmethod
    def create_notify_handler(config: ClientConfig, logger: logging.Logger) -> NotifyHandler:
        """创建基础通知处理器"""
        return NotifyHandler(config, logger)
    
    @staticmethod
    def create_dingtalk_notify_handler(config: ClientConfig, logger: logging.Logger) -> DingTalkNotifyHandler:
        """创建钉钉通知处理器"""
        return DingTalkNotifyHandler(config, logger)
    
    # 4. 数据同步Handler
    @staticmethod
    def create_sync_handler(config: ClientConfig, logger: logging.Logger) -> SyncHandler:
        """创建基础同步处理器"""
        return SyncHandler(config, logger)
    
    @staticmethod
    def create_dingtalk_sync_handler(config: ClientConfig, logger: logging.Logger) -> DingTalkSyncHandler:
        """创建钉钉同步处理器"""
        return DingTalkSyncHandler(config, logger)
    
    @staticmethod
    def create_webhook_sync_handler(config: ClientConfig, logger: logging.Logger) -> WebhookSyncHandler:
        """创建Webhook同步处理器"""
        return WebhookSyncHandler(config, logger)


# 便利函数 - 自动检测配置创建合适的处理器
def create_server_data_handler(config: ClientConfig, logger: logging.Logger) -> DataHandler:
    """
    便利函数：创建服务器数据获取处理器

    从服务器查询数据，需要客户的 api_token

    Args:
        config: 客户配置（必须包含 api-token）
        logger: 日志记录器

    Returns:
        服务器数据获取处理器实例
    """
    return CoreHandlerFactory.create_server_data_handler(config, logger)


def create_bitable_data_handler(config: ClientConfig, logger: logging.Logger) -> DataHandler:
    """
    便利函数：创建多维表格数据获取处理器

    从钉钉多维表格获取数据，使用 jss-api-extend

    Args:
        config: 客户配置（必须包含 bitable 配置）
        logger: 日志记录器

    Returns:
        多维表格数据获取处理器实例
    """
    return CoreHandlerFactory.create_bitable_data_handler(config, logger)


def create_data_handler(config: ClientConfig, logger: logging.Logger,
                       handler_type: str) -> DataHandler:
    """
    便利函数：创建数据获取处理器（需要明确指定类型）

    Args:
        config: 客户配置
        logger: 日志记录器
        handler_type: Handler类型 ("server" 或 "bitable")

    Returns:
        数据获取处理器实例

    Raises:
        ValueError: 当handler_type不是 "server" 或 "bitable" 时
    """
    if handler_type == "server":
        return CoreHandlerFactory.create_server_data_handler(config, logger)
    elif handler_type == "bitable":
        return CoreHandlerFactory.create_bitable_data_handler(config, logger)
    else:
        raise ValueError(f"Unknown handler type: {handler_type}. Must be 'server' or 'bitable'.")


def create_update_handler(config: ClientConfig, logger: logging.Logger, use_database: bool = False) -> UpdateHandler:
    """
    便利函数：创建更新处理器
    
    Args:
        config: 客户配置
        logger: 日志记录器
        use_database: 是否使用数据库版本
        
    Returns:
        更新处理器实例
    """
    if use_database:
        return CoreHandlerFactory.create_database_update_handler(config, logger)
    else:
        return CoreHandlerFactory.create_update_handler(config, logger)


def create_notify_handler(config: ClientConfig, logger: logging.Logger) -> NotifyHandler:
    """
    便利函数：创建通知处理器
    
    Args:
        config: 客户配置
        logger: 日志记录器
        
    Returns:
        通知处理器实例
    """
    # 默认使用钉钉通知处理器
    return CoreHandlerFactory.create_dingtalk_notify_handler(config, logger)


def create_sync_handler(config: ClientConfig, logger: logging.Logger) -> SyncHandler:
    """
    便利函数：创建同步处理器
    
    Args:
        config: 客户配置
        logger: 日志记录器
        
    Returns:
        同步处理器实例
    """
    # 自动检测同步类型
    bitable_config = config.bitable_config or {}
    if bitable_config.get('webhook'):
        return CoreHandlerFactory.create_webhook_sync_handler(config, logger)
    else:
        return CoreHandlerFactory.create_dingtalk_sync_handler(config, logger)


class DefaultChainBuilder:
    """
    默认的责任链构建器
    
    提供标准的4个Handler组合方式
    """
    
    @staticmethod
    def build_standard_chain(config: ClientConfig, logger: logging.Logger):
        """
        构建标准处理链
        
        包含：数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步
        
        Args:
            config: 客户配置
            logger: 日志记录器
            
        Returns:
            处理链的第一个处理器
        """
        # 注意：客户必须明确指定数据源类型
        raise ValueError("build_standard_chain() 已废弃，请使用 build_chain() 并明确指定数据源类型")
        
        # 构建责任链：数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步
        data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)
        
        logger.info("Built standard processing chain: Data -> Update -> Notify -> Sync")
        return data_handler
    
    @staticmethod
    def build_basic_chain(config: ClientConfig, logger: logging.Logger):
        """
        构建基础处理链（不包含通知）
        
        包含：数据获取 -> 更新逻辑 -> 数据同步
        
        Args:
            config: 客户配置
            logger: 日志记录器
            
        Returns:
            处理链的第一个处理器
        """
        # 注意：客户必须明确指定数据源类型
        raise ValueError("build_basic_chain() 已废弃，请使用 build_chain() 并明确指定数据源类型")
        
        # 构建责任链：数据获取 -> 更新逻辑 -> 数据同步
        data_handler.set_next(update_handler).set_next(sync_handler)
        
        logger.info("Built basic processing chain: Data -> Update -> Sync")
        return data_handler
    
    @staticmethod
    def build_monitoring_chain(config: ClientConfig, logger: logging.Logger):
        """
        构建监控链（只更新，不同步）
        
        包含：数据获取 -> 更新逻辑 -> 通知逻辑
        
        Args:
            config: 客户配置
            logger: 日志记录器
            
        Returns:
            处理链的第一个处理器
        """
        # 注意：客户必须明确指定数据源类型
        raise ValueError("build_monitoring_chain() 已废弃，请使用 build_chain() 并明确指定数据源类型")
        
        # 构建责任链：数据获取 -> 更新逻辑 -> 通知逻辑
        data_handler.set_next(update_handler).set_next(notify_handler)
        
        logger.info("Built monitoring chain: Data -> Update -> Notify")
        return data_handler


# 最简单的使用方式
def build_chain(config: ClientConfig, logger: logging.Logger,
               data_source: str, chain_type: str = 'standard'):
    """
    便利函数：构建处理链

    Args:
        config: 客户配置
        logger: 日志记录器
        data_source: 数据源类型 ("server" 或 "bitable")，必须明确指定
        chain_type: 链类型 ('standard', 'basic', 'monitoring')

    Returns:
        处理链的第一个处理器

    Raises:
        ValueError: 当data_source不是 "server" 或 "bitable" 时
    """
    if data_source not in ["server", "bitable"]:
        raise ValueError(f"data_source must be 'server' or 'bitable', got: {data_source}")

    if chain_type == 'standard':
        # 标准链：数据获取 -> 更新逻辑 -> 通知逻辑 -> 数据同步
        data_handler = create_data_handler(config, logger, data_source)
        update_handler = create_update_handler(config, logger, use_database=True)
        notify_handler = create_notify_handler(config, logger)
        sync_handler = create_sync_handler(config, logger)

        data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)
        logger.info(f"Built standard chain with {data_source} data source: Data -> Update -> Notify -> Sync")
        return data_handler

    elif chain_type == 'basic':
        # 基础链：数据获取 -> 更新逻辑 -> 数据同步（无通知）
        data_handler = create_data_handler(config, logger, data_source)
        update_handler = create_update_handler(config, logger)
        sync_handler = create_sync_handler(config, logger)

        data_handler.set_next(update_handler).set_next(sync_handler)
        logger.info(f"Built basic chain with {data_source} data source: Data -> Update -> Sync")
        return data_handler

    elif chain_type == 'monitoring':
        # 监控链：数据获取 -> 更新逻辑 -> 通知逻辑（无同步）
        data_handler = create_data_handler(config, logger, data_source)
        update_handler = create_update_handler(config, logger)
        notify_handler = create_notify_handler(config, logger)

        data_handler.set_next(update_handler).set_next(notify_handler)
        logger.info(f"Built monitoring chain with {data_source} data source: Data -> Update -> Notify")
        return data_handler

    else:
        raise ValueError(f"Unknown chain type: {chain_type}")


def create_pipeline_context(config: ClientConfig, batch_id: str = None):
    """
    便利函数：创建Pipeline上下文

    Args:
        config: 客户配置
        batch_id: 批次ID，如果为None则自动生成

    Returns:
        Pipeline上下文实例
    """
    from ..pipeline_context import PipelineContext
    return PipelineContext.create_from_config(config, batch_id)
        return DefaultChainBuilder.build_standard_chain(config, logger)
