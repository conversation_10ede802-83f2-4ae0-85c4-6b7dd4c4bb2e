#!/usr/bin/env python3
"""
简化使用示例 - 展示重构后的简单扩展方式。

这个示例展示了：
1. 如何直接继承核心处理器
2. 如何组合处理器构建链
3. 如何使用简化的工厂函数
4. 客户扩展的最佳实践
"""

import sys
import logging
from datetime import datetime

# Add parent directory to path for imports
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.client_config import ClientConfig
from core.pipeline_context import PipelineContext
from core.chain_executor import ChainExecutor
from core.handlers import (
    # 核心处理器 - 客户直接继承这些
    WorkUpdaterHandler,
    DingTalkNotification,
    WebhookDataSync,
    # 简化的工厂函数
    create_data_fetcher,
    create_work_updater,
    create_data_sync,
    create_notification,
    # 默认链构建器
    DefaultChainBuilder,
    CoreHandlerFactory,
)


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    return logging.getLogger("simplified_usage_example")


def example_1_direct_inheritance():
    """
    示例1：直接继承核心处理器（推荐方式）
    """
    print("=" * 60)
    print("示例1：直接继承核心处理器")
    print("=" * 60)

    logger = setup_logging()

    # 1. 定义客户特定的处理器
    class MyCustomWorkUpdater(WorkUpdaterHandler):
        """我的自定义作品更新器"""

        def _process_single_task(self, context, task):
            self.logger.info(f"MyCustom processing: {task.work_url}")

            # 添加自定义逻辑
            result = super()._process_single_task(context, task)

            # 自定义后处理
            if result.success and result.task.author_work:
                if not result.task.metadata:
                    result.task.metadata = {}
                result.task.metadata["custom_processed"] = True
                result.task.metadata["custom_score"] = self._calculate_score(
                    result.task.author_work
                )

            return result

        def _calculate_score(self, work):
            """计算自定义评分"""
            like_count = getattr(work, "like_count", 0)
            comment_count = getattr(work, "comment_count", 0)
            return (like_count * 0.5) + (comment_count * 2.0)

    # 2. 创建配置
    config_data = {
        "name": "my-custom-client",
        "user_info": {"api-token": "test-token"},
        "bitable": {"dentryUuid": "test-uuid", "idOrName": "test-table"},
    }
    config = ClientConfig.from_dict(config_data)

    # 3. 直接组合处理器构建链
    data_fetcher = create_data_fetcher(config, logger)
    work_updater = MyCustomWorkUpdater(config, logger)  # 直接实例化自定义类
    data_sync = create_data_sync(config, logger)
    notification = create_notification(config, logger)

    # 4. 构建责任链
    chain_head = data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification)

    print("✅ 成功构建自定义处理链")
    print(f"   数据获取器: {data_fetcher.__class__.__name__}")
    print(f"   作品更新器: {work_updater.__class__.__name__} (自定义)")
    print(f"   数据同步器: {data_sync.__class__.__name__}")
    print(f"   通知处理器: {notification.__class__.__name__}")

    return chain_head


def example_2_using_convenience_functions():
    """
    示例2：使用便利函数（适合标准需求）
    """
    print("\n" + "=" * 60)
    print("示例2：使用便利函数")
    print("=" * 60)

    logger = setup_logging()

    # 1. 创建配置
    config_data = {
        "name": "standard-client",
        "user_info": {"api-token": "test-token"},
        "bitable": {"dentryUuid": "test-uuid", "idOrName": "test-table"},
    }
    config = ClientConfig.from_dict(config_data)

    # 2. 使用便利函数快速创建标准链
    chain_head = DefaultChainBuilder.build_standard_chain(config, logger)

    print("✅ 使用默认构建器创建标准链")

    # 3. 或者使用单独的便利函数
    data_fetcher = create_data_fetcher(config, logger)
    work_updater = create_work_updater(config, logger, "database")  # 指定类型
    data_sync = create_data_sync(config, logger)  # 自动检测类型
    notification = create_notification(config, logger)  # 自动检测类型

    # 构建链
    manual_chain = data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification)

    print("✅ 使用便利函数手动构建链")
    print(f"   数据获取器: {data_fetcher.__class__.__name__}")
    print(f"   作品更新器: {work_updater.__class__.__name__}")
    print(f"   数据同步器: {data_sync.__class__.__name__}")
    print(f"   通知处理器: {notification.__class__.__name__}")

    return manual_chain


def example_3_core_factory_usage():
    """
    示例3：使用核心工厂（最大灵活性）
    """
    print("\n" + "=" * 60)
    print("示例3：使用核心工厂")
    print("=" * 60)

    logger = setup_logging()

    # 1. 创建配置
    config_data = {
        "name": "factory-client",
        "user_info": {"api-token": "test-token"},
        "bitable": {"dentryUuid": "test-uuid", "idOrName": "test-table"},
    }
    config = ClientConfig.from_dict(config_data)

    # 2. 使用核心工厂精确控制每个处理器
    data_fetcher = CoreHandlerFactory.create_dingtalk_data_fetcher(config, logger)
    work_updater = CoreHandlerFactory.create_database_work_updater(config, logger)
    data_sync = CoreHandlerFactory.create_webhook_data_sync(config, logger)
    notification = CoreHandlerFactory.create_dingtalk_notification(config, logger)

    # 3. 构建链
    chain_head = data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification)

    print("✅ 使用核心工厂创建精确控制的链")
    print(f"   数据获取器: {data_fetcher.__class__.__name__}")
    print(f"   作品更新器: {work_updater.__class__.__name__}")
    print(f"   数据同步器: {data_sync.__class__.__name__}")
    print(f"   通知处理器: {notification.__class__.__name__}")

    return chain_head


def example_4_client_specific_chain_builder():
    """
    示例4：客户特定的链构建器（推荐给复杂客户）
    """
    print("\n" + "=" * 60)
    print("示例4：客户特定的链构建器")
    print("=" * 60)

    logger = setup_logging()

    # 1. 定义客户特定的链构建器
    class MyClientChainBuilder:
        """我的客户链构建器"""

        @staticmethod
        def build_production_chain(config, logger):
            """生产环境链"""

            # 自定义处理器
            class MyWorkUpdater(WorkUpdaterHandler):
                def _process_single_task(self, context, task):
                    self.logger.info("MyClient production processing")
                    return super()._process_single_task(context, task)

            # 组合链
            data_fetcher = CoreHandlerFactory.create_dingtalk_data_fetcher(config, logger)
            work_updater = MyWorkUpdater(config, logger)
            data_sync = CoreHandlerFactory.create_dingtalk_data_sync(config, logger)  # 生产用API
            notification = CoreHandlerFactory.create_database_dingtalk_notification(config, logger)

            return data_fetcher.set_next(work_updater).set_next(data_sync).set_next(notification)

        @staticmethod
        def build_testing_chain(config, logger):
            """测试环境链"""
            data_fetcher = CoreHandlerFactory.create_dingtalk_data_fetcher(config, logger)
            work_updater = CoreHandlerFactory.create_basic_work_updater(config, logger)
            data_sync = CoreHandlerFactory.create_webhook_data_sync(config, logger)  # 测试用webhook
            # 测试环境不发通知

            return data_fetcher.set_next(work_updater).set_next(data_sync)

    # 2. 创建配置
    config_data = {
        "name": "my-client",
        "user_info": {"api-token": "test-token"},
        "bitable": {"dentryUuid": "test-uuid", "idOrName": "test-table"},
    }
    config = ClientConfig.from_dict(config_data)

    # 3. 使用客户构建器
    production_chain = MyClientChainBuilder.build_production_chain(config, logger)
    testing_chain = MyClientChainBuilder.build_testing_chain(config, logger)

    print("✅ 客户特定链构建器创建成功")
    print("   生产环境链: 数据获取 -> 自定义更新 -> API同步 -> 数据库通知")
    print("   测试环境链: 数据获取 -> 基础更新 -> Webhook同步")

    return production_chain


def example_5_real_world_usage():
    """
    示例5：真实世界的使用场景
    """
    print("\n" + "=" * 60)
    print("示例5：真实世界使用场景")
    print("=" * 60)

    logger = setup_logging()

    # 1. 加载客户配置（实际中从文件或数据库加载）
    config_data = {
        "name": "real-client",
        "data_fetcher": {"source_type": "dingtalk_bitable"},
        "work_updater": {"platforms": ["xhs", "dy"], "batch_size": 50},
        "data_sync": {"sync_type": "webhook", "batch_size": 20},
        "notification": {"enabled_channels": ["dingtalk"], "use_database": True},
        "user_info": {"api-token": "real-token"},
        "bitable": {
            "dentryUuid": "real-uuid",
            "idOrName": "real-table",
            "webhook": "https://real-webhook.com",
        },
    }
    config = ClientConfig.from_dict(config_data)

    # 2. 根据配置构建链
    try:
        # 使用便利函数，自动根据配置选择合适的实现
        chain_head = DefaultChainBuilder.build_standard_chain(config, logger)

        # 3. 创建执行器并运行
        executor = ChainExecutor(config, logger)
        executor.set_chain(chain_head)

        # 模拟执行（实际中会处理真实数据）
        print("✅ 真实场景链构建成功，准备执行")
        print("   配置驱动的自动选择:")
        print(f"   - 数据源: {config.data_source_type}")
        print(f"   - 同步方式: webhook (自动检测)")
        print(f"   - 通知方式: dingtalk with database")

        # context = executor.execute("real-batch-001")
        # print(f"   执行结果: 处理了 {len(context.updated_works)} 个作品")

    except Exception as e:
        logger.error(f"执行失败: {e}")

    return chain_head


def compare_old_vs_new():
    """
    对比旧方式 vs 新方式
    """
    print("\n" + "=" * 60)
    print("对比：旧方式 vs 新方式")
    print("=" * 60)

    print("🔴 旧方式（复杂）:")
    print("   1. 需要在工厂注册表中注册新类型")
    print("   2. 修改配置文件指定类型")
    print("   3. 通过工厂创建，不直观")
    print("   4. 扩展需要修改核心代码")
    print()
    print("   # 旧方式代码")
    print("   DataFetcherFactory.register_fetcher('custom', CustomFetcher)")
    print("   fetcher = DataFetcherFactory.create_fetcher(config, logger, 'custom')")
    print()

    print("🟢 新方式（简单）:")
    print("   1. 直接继承核心处理器")
    print("   2. 直接实例化自定义类")
    print("   3. 自由组合构建链")
    print("   4. 客户代码完全独立")
    print()
    print("   # 新方式代码")
    print("   class MyFetcher(DataFetcherHandler): pass")
    print("   fetcher = MyFetcher(config, logger)")
    print("   chain = fetcher.set_next(updater).set_next(sync)")
    print()

    print("✅ 新方式的优势:")
    print("   - 🎯 直观：直接看到要继承的类")
    print("   - 🔧 灵活：自由组合处理器")
    print("   - 📋 简单：减少抽象层")
    print("   - 🚀 易扩展：客户代码独立")
    print("   - 🛡️ 安全：不需要修改核心代码")


def main():
    """Main function demonstrating all examples."""
    print("🚀 简化使用示例 - 重构后的扩展方式")

    try:
        # 运行所有示例
        example_1_direct_inheritance()
        example_2_using_convenience_functions()
        example_3_core_factory_usage()
        example_4_client_specific_chain_builder()
        example_5_real_world_usage()
        compare_old_vs_new()

        print("\n" + "=" * 60)
        print("🎉 所有示例运行成功！")
        print("=" * 60)
        print()
        print("📚 推荐的扩展方式:")
        print("   1. 对于简单需求：使用便利函数 (示例2)")
        print("   2. 对于自定义需求：直接继承 (示例1)")
        print("   3. 对于复杂客户：创建链构建器 (示例4)")
        print("   4. 对于精确控制：使用核心工厂 (示例3)")

    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return False

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
