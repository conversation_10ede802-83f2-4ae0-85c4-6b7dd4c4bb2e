"""
Work-related data models for the work monitor system.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from datetime import datetime


@dataclass
class WorkDetailTaskModel:

    id: Optional[str] = None
    work_url: str = ""
    work_id: str = ""
    platform: str = ""

    row_id: str = ""
    dentry_uuid: str = ""
    id_or_name: str = ""

    submit_user: Optional[Any] = None
    user_id: Optional[int] = None
    
    update_count: int = 0
    threshold: str = "否"
    
    author_work: Optional[Any] = None
    
    extends: Optional[Dict[str, Any]] = None

