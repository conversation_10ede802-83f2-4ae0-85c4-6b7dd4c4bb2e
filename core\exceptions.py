"""
Exception classes for the work monitor system.
"""

from typing import Op<PERSON>, Dict, Any


class WorkMonitorException(Exception):
    """Base exception for work monitor system."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class HandlerException(WorkMonitorException):
    """Exception raised by handlers during processing."""
    
    def __init__(self, handler_name: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)
        self.handler_name = handler_name


class ConfigurationException(WorkMonitorException):
    """Exception raised for configuration errors."""
    pass


class DataFetchException(HandlerException):
    """Exception raised during data fetching."""
    pass


class WorkUpdateException(HandlerException):
    """Exception raised during work updating."""
    pass


class DataSyncException(HandlerException):
    """Exception raised during data synchronization."""
    pass


class NotificationException(HandlerException):
    """Exception raised during notification sending."""
    pass


class ScheduleException(WorkMonitorException):
    """Exception raised during scheduling operations."""
    pass
