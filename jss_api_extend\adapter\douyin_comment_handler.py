# -*- coding: utf-8 -*-

from .models.comment_model import CommentModel
from ..client.qb.handler import Handler
from ..client.qb.models import Platform, Comment
from ..utils.string_utils import StringUtils
from ..utils.uuid_utils import UUIDUtils


class DouyinCommentHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.DOUYIN)

    def query_article_comment(
        self, work_id, count
    ) -> tuple[bool, None, None] | tuple[bool, list[CommentModel], list[CommentModel]]:
        """
        获取抖音作品评论

        :param work_id: 抖音作品ID
        :param count: 需要获取的评论数量
        :return: (是否成功, 所有评论对象列表, 评论列表)
        """
        try:
            all_comment_object = []
            comment_list = []

            # 获取所有一级评论
            work_url = "https://www.douyin.com/video/" + work_id

            first_level_comment_list = self._query_one_level_comments(
                work_url=work_url, count=count
            )
            if first_level_comment_list is None:
                return False, None, None

            all_comment_object.extend(first_level_comment_list)
            comment_list.extend(first_level_comment_list)
            if len(comment_list) >= count:
                return True, all_comment_object, comment_list

            # 获取子评论
            for first_item in all_comment_object:
                if first_item.has_sub == 1:
                    sub_comments = self._query_sub_level_comments(first_item.work_url)
                    first_item.sub_comments.extend(sub_comments)
                    comment_list.extend(sub_comments)

                    if len(comment_list) >= count:
                        return True, all_comment_object, comment_list

            return True, all_comment_object, comment_list
        except Exception as e:
            self.logger_.error(e)
            return False, None, None

    def _query_one_level_comments(self, work_url, count, platform="dy"):
        """
        获取一级评论

        :param work_url: 作品URL
        :param count: 评论数量
        :param platform: 平台标识
        :return: 评论列表或None
        """
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_comment(
                key=work_url, count=count
            )
            for comment in comment_list:
                work_comment_list.append(
                    self._convert_qingbo_to_model(platform, comment)
                )

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _query_sub_level_comments(self, work_url, platform="dy") -> list | None:
        """
        获取子级评论

        :param work_url: 作品URL
        :param platform: 平台标识
        :return: 子评论列表或None
        """
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_sub_comment(key=work_url)
            for comment in comment_list:
                work_comment_list.append(
                    self._convert_qingbo_to_model(platform, comment)
                )

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _convert_qingbo_to_model(self, platform, item: Comment):
        """
        将青博数据转换为CommentModel

        :param platform: 平台标识
        :param item: 青博评论数据
        :return: CommentModel对象
        """
        try:
            commenter_id = item.comment_user_id
            comment_posttime = item.comment_posttime
            comment_content = item.comment_content

            # 安全处理数字字段，确保是整数类型
            def safe_int(value, default=0):
                if isinstance(value, str):
                    try:
                        return int(value) if value.strip() else default
                    except (ValueError, AttributeError):
                        return default
                return value if isinstance(value, int) else default

            like_count = safe_int(item.comment_like_count, 0)
            has_sub = safe_int(item.has_sub, 0)
            is_sub = safe_int(item.is_sub, 0)

            comment_uuid = UUIDUtils.generate_comment_uuid(
                commenter_id, comment_content, comment_posttime
            )
            return CommentModel(
                work_url=item.news_url,
                platform=platform,
                comment_uuid=comment_uuid,
                commenter_name=item.comment_user,
                commenter_id=commenter_id,
                commenter_url="https://www.douyin.com/user/" + commenter_id,
                content=comment_content,
                publish_time=comment_posttime,
                like_count=like_count,
                location_ip=item.comment_ip_location,
                first_cid=item.first_cid,
                has_sub=has_sub,
                is_sub=is_sub,
                sec_uid=item.sec_uid,
                news_parent_id=item.news_parent_id,
            )
        except Exception as e:
            self.logger_.error(
                f"_convert_qingbo_to_model {StringUtils.obj_2_json_string(item)}, {e}"
            )

    def is_success(self, data):
        """
        检查数据是否成功

        :param data: 响应数据
        :return: 是否成功
        """
        if data is None:
            return False
        if "data" not in data:
            return False
        return True
