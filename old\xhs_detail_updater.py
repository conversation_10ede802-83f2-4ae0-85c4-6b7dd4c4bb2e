# -*- coding: utf-8 -*-

import json
from typing import List, Dict, Any, Optional

import requests

from adapter.douyin_detail_handler import Douyin<PERSON><PERSON>il<PERSON>andler
from adapter.xhs_detail_handler import XhsDetailHandler
from client.bitable.dingtalk_bitable_factory import DingtalkBitableFactory
from client.bitable.dingtalk_bitable_notifier import DingTalkBitableNotifier
from client.qb.handler import Handler
from client.qb.models import Platform
from client.service_client import ServiceClient, Environment, ApiType
from dao.model.author_work import AuthorWork
from dao.model.work_detail_refresh import WorkDetailRefreshRecord, WorkDetailRefreshTask
from dao.repository.xhs_work_detail_refresh_repository import (
    XhsWorkDetailRefreshRepository,
)
from scene.model.work_detail_task_model import WorkDetailTaskModel
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils
from utils.url_utils import UrlUtils


class XhsDetailUpdater:
    """
    小红书作品详情更新
    """

    def __init__(
            self,
            environment: Environment = Environment.LOCAL,
            user_info: dict = None,
            updater_name: str = None,
            logger_=None,
    ):

        self.logger_ = logger_
        self.updater_name: str = updater_name
        self.api_token: str = user_info.get("api-token")
        self.user_id: int = user_info.get("user_id")
        self.tenant: str = user_info.get("tenant")

        # 初始化服务客户端
        self.service_client = ServiceClient(
            environment=environment, logger_=self.logger_, api_token=self.api_token
        )
        self.handler = Handler(logger_=self.logger_, platform=Platform.XIAOHONGSHU)
        self.refresh_repository = XhsWorkDetailRefreshRepository()
        # 本次失败的转化链接
        self.covert_fail_queue = []

        if updater_name == "水云兔" or updater_name == "淘天":
            self.notifier = DingtalkBitableFactory.create_object(updater_name,logger_,updater_name)

        self.logger_.info(f"WorkDetailUpdater company : {self.updater_name} 初始化完成")

        self.dy_handler = DouyinDetailHandler(logger_)
        self.xhs_handler = XhsDetailHandler(logger_)

    async def run(self) -> Optional[List[WorkDetailTaskModel]]:

        """
        执行完整的更新流程

        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """

        try:
            self.logger_.info("---- 开始执行详情更新流程 ---")

            # 批次生成ID
            record_id = TimeUtils.get_current_ts('%Y%m%d%H%M%S')

            if self.updater_name == "水云兔" or self.updater_name == "淘天":
                task_list = self.__query_format_task_list(record_id)
            else:
                task_list = self.__query_tasks_rows(platform="all", limit=100, record_id=record_id)

            if not task_list:
                self.logger_.info("没有找到待更新的任务")
                return []

            self.logger_.info(f"==== 找到 {len(task_list)} 个待更新任务 ====")

            # 2. 插入任务记录到数据库
            self._insert_tasks_to_db_batch(task_list)

            detail_list: List[WorkDetailTaskModel] = []

            for task in task_list:
                try:
                    result: WorkDetailTaskModel = await self._process_task(task_model=task, record_id=record_id)
                    if result:
                        self.logger_.info(f"success handle work :{StringUtils.obj_2_json_string(result)}")
                        #task.update_count += 1

                        update_count = int(task.update_count)
                        update_count += 1
                        task.update_count = str(update_count)

                        detail_list.append(result)

                except Exception as e:
                    self.logger_.error(f"处理任务失败: {task}, 错误: {e}")
                    continue

            self.logger_.info(f"更新流程完成，成功处理 {len(detail_list)} 个任务")

            self.logger_.info(
                f"更新失败的链接 userId:{self.user_id},{json.dumps(self.covert_fail_queue, ensure_ascii=False, indent=2)}")
            return detail_list

        except Exception as e:
            self.logger_.error(f"执行更新流程异常: {e}")
            raise

    def __extract_task_item(self, work_url: str):
        """
        提取链接内信息：链接转化， work_id 提取， platform 信息提取
        @param work_url:
        @return:
        """
        if not work_url:
            self.logger_.error(f"work_url为空")
            return None

        _origin_url = work_url
        _normal_url = _origin_url

        self.logger_.info(f"原始 work_url: {work_url}")

        # 分享链接确认
        if UrlUtils.check_short_url(_normal_url):
            _normal_url = self.__url_redirection(_normal_url)

        if not _normal_url:
            self.logger_.error(f"链接转化失败，短链转长连接失败 :{work_url}")
            return None

        # 提取平台信息
        _platform: str = UrlUtils.select_platform(_normal_url)

        if not _platform:
            self.logger_.error(f"format fail message :{_origin_url}")
            return None

        if _platform == "xhs":
            _work_id = UrlUtils.get_xhs_work_id(_normal_url)
        else:
            _work_id = UrlUtils.get_dy_work_id(_normal_url)

        return _platform, _work_id

    def __url_redirection(self, _work_url: str) -> Optional[str]:
        """
        对短链的处理
        @param work_url:
        @return:
        """
        try:
            _response = requests.get(_work_url, allow_redirects=True, timeout=30)
            return _response.url
        except Exception as e:
            self.logger_.error(f"{e}")
            return None

    def __query_tasks_rows(self, platform: str = 'all', limit: int = 100, record_id: str = None) \
            -> Optional[List[WorkDetailTaskModel]]:
        """
        查询可更新的任务列表 (不区分平台)

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            self.logger_.info(f"查询可更新任务， 平台: {platform}, 限制条数: {limit}")
            _response = self.service_client.query_can_update(platform=platform, limit=limit)

            if not _response or not self.service_client.is_response_success(_response):
                self.logger_.warning("接口返回异常：清检查服务器")
                return None

            task_result: List[Dict] = self.service_client.get_response_data(_response)
            if not task_result:
                self.logger_.info("没有待更新的任务")
                return None

            self.logger_.info(f"成功获取 {len(task_result)} 个任务")

            _format_task_list = []
            for task in task_result:

                _work_url = task.get("workUrl")
                _work_id = task.get("workId")
                _submit_user = task.get("submitUser", [])
                _row_id = task.get("rowId")
                _submit_user = task.get("submitTime")
                _user_id = task.get("userId")
                _id = task.get("id")

                __task_info = task.get("tableInfo", {})
                __dentry_uuid = __task_info.get("dentry_uuid")
                __id_or_name = __task_info.get("id_or_name")

                result = self.__extract_task_item(work_url=_work_url)
                if not result:
                    continue

                _platform, _work_id = result
                if not _platform or not _work_id:
                    self.logger_.warning(
                        f"提取到的 platform 或 work_id 为空，跳过。Platform: {_platform}, WorkId: {_work_id}\n")
                    continue

                work_detail = WorkDetailTaskModel(id=_id,
                                                  record_id=record_id,
                                                  work_url=_work_url,
                                                  work_id=_work_id,
                                                  submit_user=_submit_user,
                                                  row_id=_row_id,
                                                  platform=_platform,
                                                  id_or_name=__id_or_name,
                                                  dentry_uuid=__dentry_uuid,
                                                  update_count=0)
                _format_task_list.append(work_detail)

            return _format_task_list

        except Exception as e:
            self.logger_.error(f"查询任务列表异常: {e}")
            return []

    def __query_update_table_one(self) -> Optional[Dict[str, Any]]:
        """
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            _response = self.service_client.query_table_to_update()
            if not _response:
                self.logger_.warning("查询任务列表失败：响应为空")
                return None

            # 检查响应是否成功
            if not self.service_client.is_response_success(_response):
                self.logger_.error(f"查询任务列表失败：{_response.get('msg', '未知错误')}")
                return None

            # 构造数据表
            __table_info: dict = _response.get("result", {})

            if not __table_info:
                self.logger_.info("没有待更新的任务")
                return None

            self.logger_.info(f"成功获取任务: {__table_info} ")
            return __table_info

        except Exception as e:
            self.logger_.error(f"查询任务列表异常: {e}")
            return None

    def __query_format_task_list(self, record_id: str) -> Optional[List[WorkDetailTaskModel]]:
        """
            构建任务列表
            @param table_info:
            @return:
        """
        table_info: dict = self.__query_update_table_one()
        if not table_info:
            return None

        __dentry_uuid = table_info.get("dentryUuid")
        __id_or_name = table_info.get("idOrName")
        __max_update_times = table_info.get("updateTimes")

        if not all([__dentry_uuid, __id_or_name, __max_update_times]):
            self.logger_.error(f"服务端返回数据不完整 返回: {table_info}")
            return None

        self.logger_.info(
            f"开始更新: dentry_uuid：{__dentry_uuid},id_or_name：{__id_or_name}, max_update_times：{__max_update_times}"
        )

        record_list = self.notifier.list_bitable_data_by_api_filter(__dentry_uuid, __id_or_name, __max_update_times)

        if not record_list:
            self.logger_.error(f"数据表 :{__dentry_uuid} 无数据")
            return None

        task_list = []
        for record in record_list:
            result = self.__extract_task_item(work_url=record.work_url)
            if result is None:
                self.covert_fail_queue.append(record.work_url)
                continue

            platform, work_id = result
            if not platform or not work_id:
                self.logger_.warning(
                    f"提取到的 platform 或 work_id 为空，跳过。Platform: {platform}, WorkId: {work_id}\n")
                continue

            _work_task: WorkDetailTaskModel = WorkDetailTaskModel(
                id=None,
                record_id=record_id,
                work_url=record.work_url,
                platform=platform,
                work_id=work_id,
                row_id=record.row_id,
                submit_user=record.user_union_id,
                dentry_uuid=__dentry_uuid,
                id_or_name=__id_or_name,
                update_count=record.update_count,
                extends=record.extends
            )

            task_list.append(_work_task)
        return task_list

    def _insert_tasks_to_db_batch(self, tasks: List[WorkDetailTaskModel]) -> Optional[int]:
        """
        将任务插入到数据库

        Args:
            tasks: 任务列表
        """

        try:
            _task_repo_model_list: List[WorkDetailRefreshTask] = []
            for task in tasks:
                _task_repo_model = self._covert_task_to_repo_model(task)
                if not _task_repo_model:
                    continue

                _task_repo_model_list.append(_task_repo_model)
            task_num: int = self.refresh_repository.batch_insert_refresh_tasks(_task_repo_model_list)
            self.logger_.info(f"=== 成功插入任务 {task_num} 条 ===")
            return task_num
        except Exception as e:
            self.logger_.error(f"插入任务到数据库异常: {e}")
            raise

    async def _process_task(self, task_model: WorkDetailTaskModel, record_id: str) \
            -> Optional[WorkDetailTaskModel]:
        """
        处理单个任务
        """

        try:
            global _work_detail

            _id = task_model.id
            _platform = task_model.platform
            _work_id = task_model.work_id

            self.logger_.info(f"开始处理任务: work_url :{task_model.work_url}")

            # _response = self.service_client.invoke_api_quota(
            #     user_id=self.user_id,tenant= self.tenant,api_type=ApiType.qb_api_detail, platform_type=_platform
            # )
            # if not _response or not self.service_client.is_response_success(_response):
            #     self.logger_.error(f"调用接口失败: {_response}")
            #     return None

            if _platform == "dy":
                _work_detail = await self.dy_handler.query_article_detail(_work_id)
            elif _platform == "xhs":
                _work_detail = self.xhs_handler.query_article_detail(_work_id)
            else:
                self.logger_.error(f"未知的平台: {_platform}")
                self.covert_fail_queue.append(_work_id)
                return None

            if not _work_detail:
                self.logger_.error(f"获取作品详情失败: work_url={task_model.work_url}")
                self.covert_fail_queue.append(task_model.work_url)
                return None
            # 详情
            task_model.author_work = _work_detail

            __db_record = self._covert_author_work_to_db_record(
                record_id=record_id,
                origin_work_url=task_model.work_url,
                platform=_platform,
                author_work=_work_detail
            )
            self.logger_.info(f"成功获取作品详情: work_id={_work_id}")
            record_id_db = self.refresh_repository.insert_refresh_record(__db_record)

            self.refresh_repository.update_refresh_task_status(record_id=record_id, work_id=_work_id, status=1)
            self.logger_.info(f" == 更新任务状态成功 == record_id：{record_id}, work_id:{_work_id}")

            # if _id:
            #     mark_result = self.service_client.mark_update(_id)
            #     if mark_result and self.service_client.is_response_success(mark_result):
            #         self.logger_.info(f"成功标记任务完成: id={_id}")
            #     else:
            #         self.logger_.warning(f"标记任务完成失败: id={_id}")

            self.logger_.info(f"任务处理完成: {StringUtils.obj_2_json_string(task_model)}")
            return task_model

        except Exception as e:
            self.logger_.error(f"处理任务异常: {StringUtils.obj_2_json_string(task_model)}, 错误: {e}")
            return None

    def _covert_author_work_to_db_record(
            self,
            record_id: str,
            origin_work_url: str,
            platform: str,
            author_work: AuthorWork
    ) -> Optional[WorkDetailRefreshRecord]:
        """
            构建记录
        """
        current_time = TimeUtils.get_current_ts()

        return WorkDetailRefreshRecord(
            id=None,
            record_id=record_id,
            platform=platform,
            author_id=author_work.author_id,
            author_identity=author_work.author_identity,
            author_avatar=author_work.author_avatar,
            author_name=author_work.author_name,
            author_url=author_work.author_url,
            work_id=author_work.work_id,
            work_uuid=author_work.work_uuid,
            url=author_work.url,
            download_url=author_work.download_url,
            long_url=origin_work_url,
            digest=author_work.digest,
            title=author_work.title,
            thumbnail_link=author_work.thumbnail_link,
            content=author_work.content,
            img_urls=author_work.img_urls,
            video_urls=author_work.video_urls,
            music_url=author_work.music_url,
            music_author_name=author_work.music_author_name,
            music_id=author_work.music_id,
            music_name=author_work.music_name,
            publish_time=author_work.publish_time,
            publish_day=author_work.publish_day,
            location_ip=author_work.location_ip,
            read_count=author_work.read_count,
            like_count=author_work.like_count,
            comment_count=author_work.comment_count,
            share_count=author_work.share_count,
            collect_count=author_work.collect_count,
            record_time=current_time,
            is_del=0,
        )

    def _covert_task_to_repo_model(self, task: WorkDetailTaskModel) \
            -> Optional[WorkDetailRefreshTask]:
        """
        task model 转为数据存储
        :param task:
        :return:
        """
        try:
            return WorkDetailRefreshTask(
                id=None,
                record_id=task.record_id,
                user_id=self.user_id,
                work_url=task.work_url,
                work_id=task.work_id,
                platform=task.platform,
                row_id=task.row_id,
                table_info=json.dumps({
                    "id_or_name": task.id_or_name,
                    "dentry_uuid": task.dentry_uuid
                })
            )
        except Exception as e:
            self.logger_.error(f"转换任务失败: {task}, 错误: {e}")
            return None

    def __invoke_api_quota(self, api_type: ApiType, api_platform: str) -> Optional[bool]:
        """
        调用接口配额
        """
        try:
            _response = self.service_client.invoke_api_quota(
                self.user_id, self.tenant, api_type, api_platform
            )
            if not _response or not self.service_client.is_response_success(_response):
                self.logger_.error(f"调用接口失败: {_response}")
                return False

            return True
        except Exception as e:
            self.logger_.error(f"调用接口失败: {e}")
            return False


# 使用示例
if __name__ == "__main__":
    from common.env import Env
    logger_ = Env().get_main_logger()
    from jss_api_extend import *
    xhs_ =  XhsDetailHandler(logger_)
    au : AuthorWork  = xhs_.query_article_detail("686e6a68000000002203d213")
    print(au.title)

    pass
    # 创建更新器实例
    # updater = XhsDetailUpdater(environment=Environment.LOCAL, platform=Platform.XIAOHONGSHU)

    # 执行更新流程
    # results = updater.run()
    #
    # print(f"处理完成，共处理 {len(results)} 个任务")
