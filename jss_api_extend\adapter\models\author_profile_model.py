class AuthorProfileModel:
    def __init__(
        self,
        signature: str,
        sec_uid: str,
        nickname: str,
        work_count: int,
        ip_location: str,
        total_favorited: int,
        follower_count: int,
        following_count: int,
        favoriting_count: int,
        gender: int,
    ):
        """
        signature #签名
        sec_uid 唯一ID
        nickname 名称
        work_count 作品个数
        ip_location IP地址
        total_favorited 总点赞数
        follower_count 总粉丝数
        following_count 作者关注数
        favoriting_count 作者喜欢作品个数
        gender 性别 0 女 1 男

        """
        self.signature = signature
        self.sec_uid = sec_uid
        self.home_url = "https://www.douyin.com/user/" + sec_uid
        self.nickname = nickname
        self.work_count = work_count
        self.ip_location = ip_location
        self.total_favorited = total_favorited
        self.follower_count = follower_count
        self.following_count = following_count
        self.favoriting_count = favoriting_count
        self.gender = gender
