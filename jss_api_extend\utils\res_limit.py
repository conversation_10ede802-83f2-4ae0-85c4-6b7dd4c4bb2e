# -*- coding: utf-8 -*-

import sys


class ResLimit:
    @staticmethod
    def limit_memory(maxsize):
        if sys.platform.startswith('win'):
            return

        try:
            import resource
            soft, hard = resource.getrlimit(resource.RLIMIT_AS)
            resource.setrlimit(resource.RLIMIT_AS, (maxsize, hard))
        except ImportError:
            # resource module is not available on Windows
            print("Warning: resource module not available, memory limit not set")
