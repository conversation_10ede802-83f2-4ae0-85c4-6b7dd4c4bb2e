# -*- encoding:utf-8 -*-


class ErrorCode:
    # success
    Code_Success = 0
    # 未知错误
    Code_Error_unknown = -1
    # 参数错误
    Code_Error_params = 50001
    Code_Error_invalid_date = 50001
    Code_Error_invalid_capital = 50002
    Code_Error_invalid_interval = 50003
    Code_Error_invalid_matching_type = 50004
    Code_Error_invalid_contracts = 50005
    Code_Error_user_permission_tick = 50006
    Code_Error_user_permission_time_interval = 50007
    Code_Error_frozen_volume = 50008
    Code_Error_valid_price = 50009
    Code_Error_order = 50010
    Code_Error_invalid_token = 50011
    Code_Error_add_task = 50012
    Code_Error_run_task = 50013
    Code_Error_order_pos = 50015

    Code_Error_instrument_id = 50100
    Code_Error_read_data = 50101
    Code_Error_unknown_instrument = 50102


    error_message_map = {
        Code_Error_unknown: '未知错误',
        Code_Error_params: '参数错误',
        Code_Error_invalid_date: '错误的日期',
        Code_Error_invalid_capital: '错误的初始资金',
        Code_Error_invalid_interval: '错误的backtesting mode',
        Code_Error_invalid_matching_type: '错误的matching type',
        Code_Error_invalid_contracts: '合约列表为空',
        Code_Error_user_permission_tick: '用户无权限使用tick回测',
        Code_Error_user_permission_time_interval: '超过用户回测最大时间段',
        Code_Error_frozen_volume: "冻结持仓数量小于0",
        Code_Error_valid_price: "无效的价格、时间段",
        Code_Error_order: "无效的订单",
        Code_Error_invalid_token: "令牌不能为空",
        Code_Error_add_task: "添加任务失败",
        Code_Error_run_task: "添加任务失败",
        Code_Error_order_pos: "订单中的pos数量转换错误",
        Code_Error_instrument_id: "未找到指定合约",
        Code_Error_read_data: "读取行情数据失败",
        Code_Error_unknown_instrument: "未知的合约"
    }

    @staticmethod
    def get_error_msg(error_code: int, error_msg=None):
        if error_msg is None:
            return ErrorCode.error_message_map.get(error_code, 'unknown')
        else:
            return error_msg
