"""
数据获取器工厂类

根据客户配置自动创建合适的数据获取器，支持：
1. 默认数据获取器
2. 客户特定的数据获取器
3. 基于配置的动态创建

使用方式：
```python
from core.handlers.data_fetcher_factory import DataFetcherFactory

# 创建数据获取器
fetcher = DataFetcherFactory.create_fetcher(client_config, logger)

# 创建DataHandler
handler = DataHandler(client_config, logger, data_fetcher=fetcher)
```
"""

from typing import Optional
from config.client_config import ClientConfig
from .data_handler import ClientSpecificDataFetcher, DefaultDataFetcher
from .client_data_fetchers import (
    <PERSON><PERSON><PERSON>tuDataFetcher,
    TaotianDataFetcher, 
    DamaoDataFetcher
)


class DataFetcherFactory:
    """
    数据获取器工厂类
    
    根据客户名称和配置创建合适的数据获取器
    """
    
    # 客户名称到数据获取器类的映射
    CLIENT_FETCHER_MAP = {
        "水云兔": ShuiyuntuDataFetcher,
        "shuiyuntu": <PERSON><PERSON><PERSON>tuDataFetcher,
        "淘天": TaotianDataFetcher,
        "taotian": TaotianDataFetcher,
        "大毛": DamaoDataFetcher,
        "damao": DamaoDataFetcher,
        "大毛餐饮": DamaoDataFetcher,
    }
    
    @classmethod
    def create_fetcher(cls, client_config: ClientConfig, logger) -> ClientSpecificDataFetcher:
        """
        根据客户配置创建数据获取器
        
        Args:
            client_config: 客户配置
            logger: 日志记录器
            
        Returns:
            客户特定的数据获取器实例
        """
        client_name = client_config.name.lower().strip()
        
        # 1. 首先检查是否有客户特定的数据获取器
        fetcher_class = cls._get_fetcher_class_by_name(client_name)
        if fetcher_class:
            logger.info(f"使用客户特定数据获取器: {fetcher_class.__name__} for {client_config.name}")
            return fetcher_class(client_config, logger)
        
        # 2. 检查配置中是否指定了特定的数据获取器类型
        data_fetcher_config = client_config.data_fetcher
        fetcher_type = data_fetcher_config.get("fetcher_type", "").lower()
        
        if fetcher_type:
            fetcher_class = cls._get_fetcher_class_by_type(fetcher_type)
            if fetcher_class:
                logger.info(f"使用配置指定的数据获取器: {fetcher_class.__name__} for {client_config.name}")
                return fetcher_class(client_config, logger)
        
        # 3. 使用默认数据获取器
        logger.info(f"使用默认数据获取器: DefaultDataFetcher for {client_config.name}")
        return DefaultDataFetcher(client_config, logger)
    
    @classmethod
    def _get_fetcher_class_by_name(cls, client_name: str) -> Optional[type]:
        """根据客户名称获取数据获取器类"""
        # 精确匹配
        if client_name in cls.CLIENT_FETCHER_MAP:
            return cls.CLIENT_FETCHER_MAP[client_name]
        
        # 模糊匹配
        for key, fetcher_class in cls.CLIENT_FETCHER_MAP.items():
            if key.lower() in client_name or client_name in key.lower():
                return fetcher_class
        
        return None
    
    @classmethod
    def _get_fetcher_class_by_type(cls, fetcher_type: str) -> Optional[type]:
        """根据获取器类型获取数据获取器类"""
        type_map = {
            "shuiyuntu": ShuiyuntuDataFetcher,
            "taotian": TaotianDataFetcher,
            "damao": DamaoDataFetcher,
            "default": DefaultDataFetcher,
        }
        
        return type_map.get(fetcher_type)
    
    @classmethod
    def register_fetcher(cls, client_name: str, fetcher_class: type):
        """
        注册新的客户数据获取器
        
        Args:
            client_name: 客户名称
            fetcher_class: 数据获取器类
        """
        cls.CLIENT_FETCHER_MAP[client_name.lower()] = fetcher_class
    
    @classmethod
    def list_available_fetchers(cls) -> dict:
        """
        列出所有可用的数据获取器
        
        Returns:
            客户名称到数据获取器类名的映射
        """
        return {
            client_name: fetcher_class.__name__ 
            for client_name, fetcher_class in cls.CLIENT_FETCHER_MAP.items()
        }


class ConfigurableDataFetcher(ClientSpecificDataFetcher):
    """
    可配置的数据获取器
    
    通过配置文件完全定义过滤条件和字段映射，无需编写代码
    
    配置示例：
    ```yaml
    data_fetcher:
      fetcher_type: "configurable"
      filter_conditions:
        - field: "更新次数"
          operator: "less"
          value: [5]
        - field: "平台"
          operator: "equal"
          value: ["小红书", "抖音"]
      field_mapping:
        work_url_field: "作品链接"
        user_field: "运营人员"
        update_count_field: "更新次数"
        extends_field: "工单编号"
    ```
    """
    
    def __init__(self, client_config: ClientConfig, logger):
        super().__init__(client_config, logger)
        self.notifier = DefaultDataFetcher(client_config, logger).notifier
        self.field_mapping = client_config.data_fetcher.get("field_mapping", {})
    
    def fetch_data(self, context) -> list:
        """使用配置定义的逻辑获取数据"""
        dentry_uuid = self.client_config.bitable.get("dentryUuid")
        id_or_name = self.client_config.bitable.get("idOrName", "基础数据表")
        
        if not dentry_uuid:
            self.logger.error(f"客户 {self.client_config.name} 缺少 dentryUuid 配置")
            return []
        
        filter_conditions = self.get_filter_conditions()
        record_list = self.notifier.query_records(
            dentry_uuid, 
            id_or_name, 
            filter_conditions=filter_conditions
        )
        
        if not record_list:
            self.logger.info(f"客户 {self.client_config.name} 数据表无符合条件的数据")
            return []
        
        return self._convert_records_to_tasks(record_list, dentry_uuid, id_or_name)
    
    def get_filter_conditions(self) -> list:
        """从配置中获取过滤条件"""
        return self.client_config.data_fetcher.get("filter_conditions", [])
    
    def _convert_records_to_tasks(self, record_list, dentry_uuid: str, id_or_name: str) -> list:
        """使用配置的字段映射转换记录"""
        task_list = []
        
        # 字段映射配置
        work_url_field = self.field_mapping.get("work_url_field", "作品链接")
        user_field = self.field_mapping.get("user_field", "运营人员")
        update_count_field = self.field_mapping.get("update_count_field", "更新次数")
        extends_field = self.field_mapping.get("extends_field", "")
        
        for record in record_list:
            fields = record.get("fields", {})
            work_url = fields.get(work_url_field)
            
            if not work_url:
                continue
            
            # 提取平台和作品ID
            from .data_handler import DefaultDataFetcher
            default_fetcher = DefaultDataFetcher(self.client_config, self.logger)
            result = default_fetcher._extract_task_item(work_url)
            
            if result is None:
                self.logger.warning(f"客户 {self.client_config.name}: 无法解析作品链接 {work_url}")
                continue
            
            platform, work_id = result
            if not platform or not work_id:
                continue
            
            from models.work_models import WorkDetailTaskModel
            work_task = WorkDetailTaskModel(
                id=None,
                work_url=work_url,
                platform=platform,
                work_id=work_id,
                row_id=record.get("id"),
                submit_user=fields.get(user_field, ""),
                dentry_uuid=dentry_uuid,
                id_or_name=id_or_name,
                update_count=fields.get(update_count_field, 0),
                extends=fields.get(extends_field, "") if extends_field else ""
            )
            
            task_list.append(work_task)
        
        self.logger.info(f"客户 {self.client_config.name}: 成功转换 {len(task_list)} 个任务")
        return task_list


# 注册可配置数据获取器
DataFetcherFactory.register_fetcher("configurable", ConfigurableDataFetcher)


def create_data_handler_with_fetcher(client_config: ClientConfig, logger):
    """
    便利函数：创建带有客户特定数据获取器的DataHandler
    
    Args:
        client_config: 客户配置
        logger: 日志记录器
        
    Returns:
        配置好的DataHandler实例
    """
    from .data_handler import DataHandler
    
    # 创建客户特定的数据获取器
    fetcher = DataFetcherFactory.create_fetcher(client_config, logger)
    
    # 创建DataHandler
    return DataHandler(client_config, logger, data_fetcher=fetcher)
