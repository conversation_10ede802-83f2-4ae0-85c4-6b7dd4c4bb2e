# -*- coding: utf-8 -*-
# @Time : 2025/6/9 17:53
# <AUTHOR> cyf
# @File : xhs_work_detail_refresh_repository.py

from typing import List, Optional

from dao.db_adapter import g_database_product
from dao.model.work_detail_refresh import WorkDetailRefreshTask, WorkDetailRefreshRecord
from utils.time_utils import TimeUtils


class XhsWorkDetailRefreshRepository:
    """
    刷新记录相关的数据库操作类
    """

    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert_refresh_task(
            self, record_id: str, user_id: int, work_url: str, work_id: str, platform: str, row_id: str, table_info: str
    ) -> int:
        """
        插入刷新任务
        Args:
            task: 刷新任务对象

        Returns:
            int: 插入的ID
        """
        sql = """
            INSERT INTO work_detail_refresh_task (
                record_id, user_id, work_url, work_id, platform, status, row_id, table_info, create_time
            ) VALUES (
                %(record_id)s, %(user_id)s, %(work_url)s, %(work_id)s,
                %(platform)s, %(status)s, %(row_id)s, %(table_info)s ,%(create_time)s
            )
        """

        params = {
            "record_id": record_id,
            "user_id": user_id,
            "work_url": work_url,
            "work_id": work_id,
            "platform": platform,
            "table_info": table_info,
            "status": 0,
            "row_id": row_id,
            "create_time": TimeUtils.get_current_ts(),
        }

        return self.gdb_datasource.insert(sql, params)


    def batch_insert_refresh_tasks(self, tasks: List[WorkDetailRefreshTask]) -> int:
        """
        批量插入刷新任务

        Args:
            tasks: WorkDetailRefreshTask对象列表

        Returns:
            int: 插入的记录数量
        """
        if not tasks:
            return 0

        sql = """
            INSERT INTO work_detail_refresh_task (
                record_id, user_id, work_url, work_id, platform, status, row_id, table_info, create_time
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """

        # 将WorkDetailRefreshTask对象列表转换为参数元组列表
        args_list = []
        current_time = TimeUtils.get_current_ts()

        for task in tasks:
            # 使用任务中的create_time，如果没有则使用当前时间
            create_time = task.create_time if task.create_time else current_time

            args = (
                task.record_id,
                task.user_id,
                task.work_url,
                task.work_id,
                task.platform,
                task.status if task.status is not None else 0,  # 默认状态为0
                task.row_id,
                task.table_info,
                create_time
            )
            args_list.append(args)

        return self.gdb_datasource.batch_insert(sql, args_list)

    def update_refresh_task_status(
            self, record_id: str, work_id: str, status: int
    ) -> bool:
        """
        每次请求成功更新单条记录
        Args:
            record_id: 记录ID
            work_id: 作品ID
            status: 状态值

        Returns:
            bool: 更新是否成功
        """
        sql = "UPDATE work_detail_refresh_task SET status = %(status)s WHERE record_id  = %(record_id)s AND work_id = %(work_id)s"
        params = {"record_id": record_id, "work_id": work_id, "status": status}
        return self.gdb_datasource.execute(sql, params) > 0

    def get_refresh_task_by_record_id(
            self,
            record_id: str,
            status: int
    ) -> Optional[List[WorkDetailRefreshTask]]:
        """
        根据ID获取刷新任务列表
        Args:
            row_id: 本次运行记录ID

        Returns:
            Optional[List[WorkDetailRefreshTask]]: 任务对象列表
        """
        sql = "SELECT * FROM work_detail_refresh_task WHERE record_id = %(record_id)s and status = %(status)s"
        params = {"record_id": record_id, "status": status}
        rows = self.gdb_datasource.fetch_all(sql, params)
        if not rows:
            return None
        return [WorkDetailRefreshTask(**row) for row in rows]

    def get_pending_refresh_tasks(
            self, limit: int = 100
    ) -> List[WorkDetailRefreshTask]:
        """
        获取待处理的刷新任务
        Args:
            limit: 限制返回数量

        Returns:
            List[WorkDetailRefreshTask]: 任务列表
        """
        sql = """
            SELECT * FROM work_detail_refresh_task 
            WHERE status = 0 
            ORDER BY create_time ASC 
            LIMIT %(limit)s
        """
        params = {"limit": limit}
        rows = self.gdb_datasource.fetch_all(sql, params)
        return [WorkDetailRefreshTask(**row) for row in rows]

    def insert_refresh_record(self, record: WorkDetailRefreshRecord) -> int:
        """
        插入刷新记录
        Args:
            record: 刷新记录对象

        Returns:
            int: 插入的ID
        """
        sql = """
            INSERT INTO work_detail_refresh_record (
                record_id, platform, author_id, author_identity, author_avatar, author_name,
                author_url, work_id, work_uuid, url, download_url, long_url, 
                digest, title, thumbnail_link, content, img_urls, video_urls,
                music_url, music_author_name, music_id, music_name, publish_time,
                publish_day, location_ip, read_count, like_count, comment_count,
                share_count, collect_count, record_time, is_del
            ) VALUES (
                %(record_id)s, %(platform)s, %(author_id)s, %(author_identity)s, %(author_avatar)s,
                %(author_name)s, %(author_url)s, %(work_id)s, %(work_uuid)s, %(url)s,
                %(download_url)s, %(long_url)s, %(digest)s, %(title)s, %(thumbnail_link)s,
                %(content)s, %(img_urls)s, %(video_urls)s, %(music_url)s,
                %(music_author_name)s, %(music_id)s, %(music_name)s, %(publish_time)s,
                %(publish_day)s, %(location_ip)s, %(read_count)s, %(like_count)s,
                %(comment_count)s, %(share_count)s, %(collect_count)s, %(record_time)s,
                %(is_del)s
            )
        """
        params = {
            "record_id": record.record_id,
            "platform": record.platform,
            "author_id": record.author_id,
            "author_identity": record.author_identity,
            "author_avatar": record.author_avatar,
            "author_name": record.author_name,
            "author_url": record.author_url,
            "work_id": record.work_id,
            "work_uuid": record.work_uuid,
            "url": record.url,
            "download_url": record.download_url,
            "long_url": record.long_url,
            "digest": record.digest,
            "title": record.title,
            "thumbnail_link": record.thumbnail_link,
            "content": record.content,
            "img_urls": record.img_urls,
            "video_urls": record.video_urls,
            "music_url": record.music_url,
            "music_author_name": record.music_author_name,
            "music_id": record.music_id,
            "music_name": record.music_name,
            "publish_time": record.publish_time,
            "publish_day": record.publish_day,
            "location_ip": record.location_ip,
            "read_count": record.read_count,
            "like_count": record.like_count,
            "comment_count": record.comment_count,
            "share_count": record.share_count,
            "collect_count": record.collect_count,
            "record_time": record.record_time,
            "is_del": record.is_del,
        }
        return self.gdb_datasource.insert(sql, params)

    def get_refresh_record_by_id(
            self, row_id: str
    ) -> Optional[WorkDetailRefreshRecord]:
        """
        根据记录ID获取刷新记录
        Args:
            row_id: 记录ID

        Returns:
            Optional[WorkDetailRefreshRecord]: 记录对象
        """
        sql = "SELECT * FROM work_detail_refresh_record WHERE row_id = %(row_id)s"
        params = {"row_id": row_id}
        row = self.gdb_datasource.fetch_one(sql, params)
        if not row:
            return None
        return WorkDetailRefreshRecord(**row)
    def query_row_repeat_count(self,row_id: str, user_id: int) -> Optional[int]:
        """
            查询任务更新的次数
        """

        sql = "SELECT COUNT(id) FROM work_detail_refresh_task WHERE row_id = %s and user_id = %s and status = 1"
        result: dict = self.gdb_datasource.fetch_one(sql, (row_id, user_id))
        return result.get("COUNT(id)")