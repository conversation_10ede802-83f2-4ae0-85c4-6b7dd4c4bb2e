# -*- coding: utf-8 -*-
import argparse
import asyncio
import time
from datetime import datetime

from client.service_client import Environment
from common.env import Env
from scene.damao.work_detail_comment import WorkDetailCommentProcessor
from scene.work_detail_processor import WorkDetailProcessor

logger = Env().get_main_logger()


def parse_args():
    parser = argparse.ArgumentParser(description='小红书数据更新任务')
    parser.add_argument('--e', type=str, choices=['local', 'test', 'prod'], default='local',
                        help='运行环境: local test 或 prod')
    parser.add_argument('--c', type=str, required=True,
                        help='任务ID, 例如: 水云兔/')

    parser.add_argument('--t', type=int, default=60, help='运行时间间隔 默认 60s ')
    return parser.parse_args()


async def run_processor(env: str, company: str):
    try:
        env_map = {'test': Environment.TEST, 'prod': Environment.PRODUCTION, 'local': Environment.LOCAL}
        run_env = env_map[env.lower()]

        logger.info(f"开始执行小红书数据更新任务... [环境: {env}, 公司名称: {company}]")
        if company != "大毛":
            processor = WorkDetailProcessor(env=run_env, logger=logger, company=company)
            await processor.start_choice_job(company)
        else:
            processor = WorkDetailCommentProcessor(logger_=logger, dingtalk_key=company)
            processor.invoke()

        logger.info("小红书数据更新任务执行完成")
    except Exception as e:
        logger.error(f"执行任务时发生错误: {str(e)}")


async def main():
    args = parse_args()
    logger.info("=== 启动作品定时刷新逻辑 ===")
    interval: int = args.t
    logger.info(f"执行间隔时间为: {interval}s")
    while True:
        try:
            current_time = datetime.now()
            logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

            await run_processor(args.e, args.c)
            time.sleep(interval)

        except KeyboardInterrupt:
            logger.info("程序被手动终止")
            break
        except Exception as e:
            logger.error(f"发生未预期的错误: {str(e)}")
            time.sleep(60)


if __name__ == "__main__":
    asyncio.run(main())
