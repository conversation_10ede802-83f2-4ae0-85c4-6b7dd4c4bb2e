# -*- coding: utf-8 -*-

import datetime


class WorkComment(object):

    def __init__(self,
                 _id,
                 author_id,
                 author_name,
                 work_id,
                 work_title,
                 work_url,
                 platform,
                 comment_uuid,
                 commenter_name,
                 commenter_id,
                 commenter_url,
                 content,
                 publish_time,
                 like_count,
                 location_ip,
                 text_polarity,

                 record_time):
        self.id = _id
        self.author_id = author_id
        self.author_name = author_name
        self.work_id = work_id
        self.work_title = work_title
        self.work_url = work_url
        self.platform = platform
        self.comment_uuid = comment_uuid
        self.commenter_name = commenter_name
        self.commenter_id = commenter_id
        self.commenter_url = commenter_url
        self.content = content
        self.publish_time = publish_time
        self.like_count = like_count
        self.location_ip = location_ip
        self.text_polarity = text_polarity
        self.record_time = record_time

