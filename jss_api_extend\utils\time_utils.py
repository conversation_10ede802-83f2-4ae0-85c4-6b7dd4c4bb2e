# -*- encoding:utf-8 -*-

import time
import datetime


class TimeUtils(object):
    @staticmethod
    def time_add_delta(tm, delta):
        return (datetime.datetime.combine(datetime.date.today(), tm) + delta).time()

    @staticmethod
    def get_yst_datetime():
        today = datetime.datetime.now()
        yesterday = today - datetime.timedelta(days=1)
        return yesterday.strftime('%Y-%m-%d')

    @staticmethod
    def get_current_ts():
        n = datetime.datetime.now()
        date_str = n.strftime("%Y-%m-%d %H:%M:%S")
        return date_str

    @staticmethod
    def get_current_ts(formatter='%Y-%m-%d %H:%M:%S'):
        n = datetime.datetime.now()
        date_str = n.strftime(formatter)
        return date_str

    @staticmethod
    def dt_to_str(n, formatter='%Y-%m-%d %H:%M:%S'):
        date_str = n.strftime(formatter)
        return date_str

    @staticmethod
    def dt_str_to_ms(time_str):
        dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        timestamp_ms = int(time.mktime(dt.timetuple()) * 1000)

        return timestamp_ms

    @staticmethod
    def ts_to_str(timestamp, formatter='%Y-%m-%d %H:%M:%S'):
        try:
            # 转换为 datetime 对象
            dt = datetime.datetime.fromtimestamp(timestamp)

            # 格式化为字符串
            formatted_str = dt.strftime(formatter)

            return formatted_str
        except Exception as e:
            print(e)
            return ""

    @staticmethod
    def get_past_date(days_ago: int) -> str:
        today = datetime.date.today()
        time_delta = datetime.timedelta(days=days_ago)
        past_date = today - time_delta
        return past_date.strftime('%Y-%m-%d')

    @staticmethod
    def is_within_last_week_and_before_today(timestamp_ms: int) -> bool:
        """
        判断一个毫秒级时间戳是否在最近一周内，并且严格早于今天。
        (范围: [7天前的零点, 今天零点) )

        :param timestamp_ms: 要检查的时间戳 (毫秒格式)
        :return: 如果在指定范围内则返回 True, 否则返回 False
        """
        timestamp_sec = timestamp_ms / 1000
        try:
            input_dt = datetime.datetime.fromtimestamp(timestamp_sec)
        except (ValueError, OSError):
            print(f"错误: 无效的时间戳 {timestamp_ms}")
            return False

        # 2. 获取当前的日期时间
        now_dt = datetime.datetime.now()
        start_of_today_dt = now_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        seven_days_ago_dt = start_of_today_dt - datetime.timedelta(days=7)
        return seven_days_ago_dt <= input_dt < start_of_today_dt